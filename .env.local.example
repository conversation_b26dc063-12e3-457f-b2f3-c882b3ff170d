# ScholarAI Environment Configuration
# Copy this file to .env.local and fill in your actual values

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=https://agmytnkamjhlxpbyhwix.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFnbXl0bmthbWpobHhwYnlod2l4Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTQ0MzU4MjEsImV4cCI6MjA3MDAxMTgyMX0.P-ovz_388b8XM4zsqHLniAF4xLJ-i_hOyPg38oUAb1E
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFnbXl0bmthbWpobHhwYnlod2l4Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1NDQzNTgyMSwiZXhwIjoyMDcwMDExODIxfQ.OStI6kackzYBRne0u7pvE8BUhE0OYJgJ1PxZKqXo8tc

# Connect to Supabase via connection pooling
DATABASE_URL="postgresql://postgres.agmytnkamjhlxpbyhwix:<EMAIL>:6543/postgres?pgbouncer=true"

# Direct connection to the database. Used for migrations
DIRECT_URL="postgresql://postgres.agmytnkamjhlxpbyhwix:<EMAIL>:5432/postgres"


# Authentication Configuration
NEXTAUTH_URL=http://localhost:9002
NEXTAUTH_SECRET=f41f36ef1c6ca3da6d5365284492cf3dd7e35daa32fc862c1e7a8669d6dc462e

# Google OAuth (for authentication)
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret

# Email Server Configuration (for email-based auth)
EMAIL_SERVER_HOST=smtp.gmail.com
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER=<EMAIL>
EMAIL_SERVER_PASSWORD=your_email_password
EMAIL_FROM=<EMAIL>

# AI & Analytics
GEMINI_API_KEY=AIzaSyDxg4i7Ijn07cJjSt--oIOFh53zM6xwqnI
NEXT_PUBLIC_GOOGLE_AI_API_KEY=your_google_ai_api_key
UPSTASH_REDIS_REST_URL=your_upstash_redis_url
UPSTASH_REDIS_REST_TOKEN=your_upstash_redis_token

# Content Safety
CONTENT_SAFETY_API_KEY=your_content_safety_key
PERSPECTIVE_API_KEY=your_perspective_api_key

# Monitoring & Analytics
VERCEL_ANALYTICS_ID=your_vercel_analytics_id
SENTRY_DSN=your_sentry_dsn

# FERPA Compliance
FERPA_ENCRYPTION_KEY=your_ferpa_encryption_key
AUDIT_LOG_RETENTION_DAYS=2555 # 7 years for FERPA compliance
DATA_RETENTION_POLICY=ferpa

# Development


