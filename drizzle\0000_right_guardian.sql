DO $$ BEGIN
 CREATE TYPE "public"."account_type" AS ENUM('individual', 'school', 'district', 'organization');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "public"."age_group" AS ENUM('pre_k', 'elementary', 'middle', 'high', 'adult');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 CREATE TYPE "public"."user_role" AS ENUM('super_admin', 'org_admin', 'teacher', 'parent_guardian', 'student');
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "activity_logs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid,
	"organization_id" uuid,
	"activity_type" varchar(50) NOT NULL,
	"activity_name" varchar(255),
	"description" text,
	"entity_type" varchar(50),
	"entity_id" uuid,
	"metadata" jsonb,
	"duration_seconds" integer,
	"success" boolean DEFAULT true,
	"error_message" text,
	"ip_address" varchar(45),
	"user_agent" text,
	"session_id" uuid,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "content_analytics" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"material_id" uuid NOT NULL,
	"organization_id" uuid,
	"material_name" varchar(255) NOT NULL,
	"material_type" varchar(50) NOT NULL,
	"subject" varchar(100),
	"age_group" varchar(20),
	"analytics_date" timestamp NOT NULL,
	"period_type" varchar(20) NOT NULL,
	"view_count" integer DEFAULT 0,
	"quiz_count" integer DEFAULT 0,
	"average_quiz_score" numeric(5, 2),
	"flip_card_generations" integer DEFAULT 0,
	"difficulty_score" numeric(5, 2),
	"engagement_score" numeric(5, 2),
	"educational_value" numeric(5, 2),
	"positive_reactions" integer DEFAULT 0,
	"negative_reactions" integer DEFAULT 0,
	"safety_score" numeric(5, 2),
	"appropriateness_score" numeric(5, 2),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "learning_analytics" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"organization_id" uuid,
	"classroom_id" uuid,
	"analytics_date" timestamp NOT NULL,
	"period_type" varchar(20) NOT NULL,
	"sessions_count" integer DEFAULT 0,
	"total_time_spent_minutes" integer DEFAULT 0,
	"average_session_time_minutes" numeric(8, 2),
	"quizzes_completed" integer DEFAULT 0,
	"average_quiz_score" numeric(5, 2),
	"topics_studied" integer DEFAULT 0,
	"materials_created" integer DEFAULT 0,
	"performance_improvement" numeric(5, 2),
	"consistency_score" numeric(5, 2),
	"engagement_score" numeric(5, 2),
	"subject_performance" jsonb,
	"topics_strengths" jsonb,
	"topics_weaknesses" jsonb,
	"preferred_study_time" varchar(20),
	"average_questions_per_quiz" numeric(5, 2),
	"retry_pattern" jsonb,
	"badges_earned" integer DEFAULT 0,
	"milestones_reached" integer DEFAULT 0,
	"streak_days" integer DEFAULT 0,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "performance_metrics" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"metric_name" varchar(100) NOT NULL,
	"metric_type" varchar(50) NOT NULL,
	"endpoint" varchar(255),
	"organization_id" uuid,
	"value" numeric(12, 4) NOT NULL,
	"unit" varchar(20),
	"timestamp" timestamp NOT NULL,
	"time_window" varchar(20),
	"tags" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "usage_analytics" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"organization_id" uuid,
	"analytics_date" timestamp NOT NULL,
	"period_type" varchar(20) NOT NULL,
	"active_users" integer DEFAULT 0,
	"new_users" integer DEFAULT 0,
	"total_sessions" integer DEFAULT 0,
	"average_session_duration_minutes" numeric(8, 2),
	"quizzes_generated" integer DEFAULT 0,
	"flip_cards_generated" integer DEFAULT 0,
	"materials_uploaded" integer DEFAULT 0,
	"chat_messages_exchanged" integer DEFAULT 0,
	"average_quiz_score" numeric(5, 2),
	"completion_rate" numeric(5, 2),
	"feature_usage" jsonb,
	"popular_topics" jsonb,
	"popular_subjects" jsonb,
	"content_flagged" integer DEFAULT 0,
	"safety_violations" integer DEFAULT 0,
	"average_safety_score" numeric(5, 2),
	"api_calls" integer DEFAULT 0,
	"error_rate" numeric(5, 4),
	"average_response_time_ms" numeric(8, 2),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "audit_logs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid,
	"organization_id" uuid,
	"session_id" uuid,
	"action" varchar(100) NOT NULL,
	"entity_type" varchar(50) NOT NULL,
	"entity_id" uuid,
	"student_data_accessed" boolean DEFAULT false,
	"data_classification" varchar(50),
	"legal_basis" varchar(100),
	"description" text,
	"reason" text,
	"old_values" jsonb,
	"new_values" jsonb,
	"changed_fields" text,
	"ip_address" varchar(45),
	"user_agent" text,
	"method" varchar(10),
	"endpoint" varchar(500),
	"request_id" uuid,
	"success" boolean DEFAULT true NOT NULL,
	"error_message" text,
	"http_status" varchar(10),
	"data_retention_date" timestamp,
	"sensitive_data_masked" boolean DEFAULT true,
	"risk_level" varchar(20) DEFAULT 'low',
	"security_flags" text,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "consent_records" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"student_id" uuid,
	"organization_id" uuid,
	"consent_type" varchar(50) NOT NULL,
	"purpose" text NOT NULL,
	"data_types" text NOT NULL,
	"status" varchar(20) DEFAULT 'active' NOT NULL,
	"consent_given" boolean NOT NULL,
	"consent_date" timestamp NOT NULL,
	"withdrawn_at" timestamp,
	"withdrawn_by" uuid,
	"withdrawal_reason" text,
	"consent_method" varchar(50) NOT NULL,
	"ip_address" varchar(45),
	"document_url" text,
	"expires_at" timestamp,
	"reminder_sent_at" timestamp,
	"renewal_required" boolean DEFAULT false,
	"lawful_basis" varchar(100),
	"minor_consent_handling" varchar(50),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "data_access_requests" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"requester_id" uuid NOT NULL,
	"student_id" uuid,
	"organization_id" uuid,
	"request_type" varchar(50) NOT NULL,
	"description" text NOT NULL,
	"data_types" text NOT NULL,
	"legal_basis" varchar(100) NOT NULL,
	"educational_purpose" text,
	"status" varchar(20) DEFAULT 'pending',
	"priority" varchar(20) DEFAULT 'normal',
	"assigned_to" uuid,
	"reviewed_by" uuid,
	"reviewed_at" timestamp,
	"completed_at" timestamp,
	"response_message" text,
	"data_provided" jsonb,
	"delivery_method" varchar(50),
	"ferpa_notification_sent" boolean DEFAULT false,
	"parental_consent_required" boolean DEFAULT false,
	"parental_consent_received" boolean DEFAULT false,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"due_date" timestamp
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "data_retention_schedule" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"entity_type" varchar(50) NOT NULL,
	"entity_id" uuid NOT NULL,
	"organization_id" uuid,
	"data_classification" varchar(50) NOT NULL,
	"retention_period" varchar(50) NOT NULL,
	"legal_requirement" varchar(100),
	"created_date" timestamp NOT NULL,
	"retention_start_date" timestamp NOT NULL,
	"scheduled_deletion_date" timestamp NOT NULL,
	"status" varchar(20) DEFAULT 'active',
	"hold_reason" text,
	"hold_by" uuid,
	"hold_until" timestamp,
	"deleted_at" timestamp,
	"deleted_by" uuid,
	"deletion_method" varchar(50),
	"deletion_confirmed" boolean DEFAULT false,
	"archived_at" timestamp,
	"archive_location" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "security_incidents" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"incident_id" varchar(50) NOT NULL,
	"title" varchar(255) NOT NULL,
	"description" text NOT NULL,
	"severity" varchar(20) NOT NULL,
	"incident_type" varchar(50) NOT NULL,
	"organization_id" uuid,
	"affected_users" text,
	"affected_data_types" text,
	"detected_at" timestamp NOT NULL,
	"occurred_at" timestamp,
	"reported_at" timestamp,
	"resolved_at" timestamp,
	"reported_by" uuid,
	"assigned_to" uuid,
	"response_actions" text,
	"data_compromised" boolean DEFAULT false,
	"student_data_involved" boolean DEFAULT false,
	"estimated_records_affected" integer,
	"regulatory_notification_required" boolean DEFAULT false,
	"regulatory_notification_sent" boolean DEFAULT false,
	"parent_notification_required" boolean DEFAULT false,
	"parent_notification_sent" boolean DEFAULT false,
	"status" varchar(20) DEFAULT 'open',
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "security_incidents_incident_id_unique" UNIQUE("incident_id")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "email_verification_tokens" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"token" varchar(255) NOT NULL,
	"email" varchar(255) NOT NULL,
	"expires_at" timestamp NOT NULL,
	"verified_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "email_verification_tokens_token_unique" UNIQUE("token")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "parent_child_relations" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"parent_id" uuid NOT NULL,
	"child_id" uuid NOT NULL,
	"relationship_type" varchar(50) DEFAULT 'parent',
	"has_educational_rights" boolean DEFAULT true,
	"can_view_progress" boolean DEFAULT true,
	"can_manage_account" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"created_by" uuid
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "password_reset_tokens" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"token" varchar(255) NOT NULL,
	"expires_at" timestamp NOT NULL,
	"used_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "password_reset_tokens_token_unique" UNIQUE("token")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "user_sessions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"session_token" varchar(255) NOT NULL,
	"ip_address" varchar(45),
	"user_agent" text,
	"device_info" text,
	"expires_at" timestamp NOT NULL,
	"last_activity_at" timestamp DEFAULT now(),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"revoked_at" timestamp,
	"revoked_by" uuid,
	CONSTRAINT "user_sessions_session_token_unique" UNIQUE("session_token")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "users" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"email" varchar(255) NOT NULL,
	"email_verified" timestamp,
	"hashed_password" varchar(255),
	"first_name" varchar(100),
	"last_name" varchar(100),
	"display_name" varchar(200),
	"avatar_url" text,
	"role" "user_role" DEFAULT 'student' NOT NULL,
	"account_type" "account_type" DEFAULT 'individual' NOT NULL,
	"is_minor" boolean DEFAULT false,
	"parental_consent_given" boolean DEFAULT false,
	"parental_consent_date" timestamp,
	"consent_document" text,
	"age_group" "age_group" DEFAULT 'elementary',
	"content_filter_level" varchar(20) DEFAULT 'strict',
	"is_active" boolean DEFAULT true,
	"is_email_verified" boolean DEFAULT false,
	"last_login" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"created_by" uuid,
	"data_retention_date" timestamp,
	"ferpa_directory_info" boolean DEFAULT false,
	CONSTRAINT "users_email_unique" UNIQUE("email")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "classroom_enrollments" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"classroom_id" uuid NOT NULL,
	"user_id" uuid NOT NULL,
	"role" varchar(20) NOT NULL,
	"status" varchar(20) DEFAULT 'active',
	"enrolled_at" timestamp DEFAULT now() NOT NULL,
	"completed_at" timestamp,
	"final_grade" varchar(10),
	"notes" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"created_by" uuid
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "classrooms" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"organization_id" uuid NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"subject" varchar(100),
	"grade_level" varchar(50),
	"academic_year" varchar(20),
	"term" varchar(50),
	"is_active" boolean DEFAULT true,
	"settings" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"created_by" uuid
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "organization_invitations" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"organization_id" uuid NOT NULL,
	"email" varchar(255) NOT NULL,
	"role" varchar(50) NOT NULL,
	"token" varchar(255) NOT NULL,
	"status" varchar(20) DEFAULT 'pending',
	"expires_at" timestamp NOT NULL,
	"accepted_at" timestamp,
	"accepted_by" uuid,
	"message" text,
	"inviter_name" varchar(255),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"created_by" uuid NOT NULL,
	CONSTRAINT "organization_invitations_token_unique" UNIQUE("token")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "organization_memberships" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"organization_id" uuid NOT NULL,
	"user_id" uuid NOT NULL,
	"role" varchar(50) NOT NULL,
	"title" varchar(100),
	"department" varchar(100),
	"status" varchar(20) DEFAULT 'active',
	"joined_at" timestamp DEFAULT now() NOT NULL,
	"left_at" timestamp,
	"permissions" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"created_by" uuid
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "organizations" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"display_name" varchar(255),
	"description" text,
	"logo_url" text,
	"website_url" text,
	"type" "account_type" DEFAULT 'school' NOT NULL,
	"subdomain" varchar(100),
	"contact_email" varchar(255),
	"contact_phone" varchar(50),
	"address_line_1" varchar(255),
	"address_line_2" varchar(255),
	"city" varchar(100),
	"state" varchar(100),
	"postal_code" varchar(20),
	"country" varchar(100) DEFAULT 'US',
	"ferpa_officer_name" varchar(255),
	"ferpa_officer_email" varchar(255),
	"ferpa_officer_phone" varchar(50),
	"ferpa_annual_notification" boolean DEFAULT true,
	"subscription_tier" varchar(50) DEFAULT 'basic',
	"max_students" integer DEFAULT 100,
	"max_storage_gb" numeric(10, 2) DEFAULT '10.00',
	"require_two_factor" boolean DEFAULT false,
	"password_policy" text,
	"session_timeout_minutes" integer DEFAULT 60,
	"is_active" boolean DEFAULT true,
	"settings" text,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"created_by" uuid,
	CONSTRAINT "organizations_subdomain_unique" UNIQUE("subdomain")
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "achievements" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"organization_id" uuid,
	"badge_id" varchar(100) NOT NULL,
	"badge_name" varchar(255) NOT NULL,
	"badge_description" text,
	"badge_icon" varchar(10),
	"criteria" text,
	"score" integer,
	"total_questions" integer,
	"category" varchar(50),
	"difficulty" varchar(20),
	"is_visible" boolean DEFAULT true,
	"is_shared_with_parents" boolean DEFAULT true,
	"is_shared_with_teachers" boolean DEFAULT true,
	"unlocked_at" timestamp DEFAULT now() NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "chat_sessions" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"material_id" uuid,
	"organization_id" uuid,
	"session_title" varchar(255),
	"context" varchar(100),
	"messages" jsonb NOT NULL,
	"message_count" integer DEFAULT 0,
	"safety_flags" text,
	"appropriateness_score" numeric(5, 2),
	"duration_seconds" integer,
	"tokens_used" integer,
	"status" varchar(20) DEFAULT 'active',
	"started_at" timestamp DEFAULT now() NOT NULL,
	"ended_at" timestamp,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "flip_cards" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"material_id" uuid NOT NULL,
	"organization_id" uuid,
	"title" varchar(255) NOT NULL,
	"description" text,
	"subject" varchar(100),
	"cards" jsonb NOT NULL,
	"card_count" integer NOT NULL,
	"study_count" integer DEFAULT 0,
	"last_studied_at" timestamp,
	"is_shared_with_classroom" boolean DEFAULT false,
	"is_shared_with_organization" boolean DEFAULT false,
	"status" varchar(20) DEFAULT 'active',
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "materials" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"organization_id" uuid,
	"classroom_id" uuid,
	"name" varchar(255) NOT NULL,
	"type" varchar(50) NOT NULL,
	"description" text,
	"content" text,
	"file_url" text,
	"file_size" integer,
	"file_mime_type" varchar(100),
	"safety_status" varchar(20) DEFAULT 'pending',
	"safety_score" numeric(5, 2),
	"safety_review" text,
	"subject" varchar(100),
	"topics" text,
	"difficulty" varchar(20),
	"age_group" "age_group",
	"is_public" boolean DEFAULT false,
	"share_with_classroom" boolean DEFAULT false,
	"share_with_organization" boolean DEFAULT false,
	"status" varchar(20) DEFAULT 'active',
	"last_accessed_at" timestamp,
	"access_count" integer DEFAULT 0,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"deleted_at" timestamp
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "quiz_results" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"material_id" uuid NOT NULL,
	"organization_id" uuid,
	"classroom_id" uuid,
	"quiz_title" varchar(255),
	"material_name" varchar(255) NOT NULL,
	"subject" varchar(100),
	"score" integer NOT NULL,
	"total_questions" integer NOT NULL,
	"time_spent_seconds" integer,
	"question_results" jsonb,
	"difficulty_level" varchar(20),
	"topics_tested" text,
	"topics_strong" text,
	"topics_need_work" text,
	"performance_level" varchar(20),
	"percentage_score" numeric(5, 2),
	"is_shared_with_parents" boolean DEFAULT true,
	"is_shared_with_teachers" boolean DEFAULT true,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE IF NOT EXISTS "student_profiles" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"user_id" uuid NOT NULL,
	"organization_id" uuid,
	"student_id" varchar(100),
	"grade_level" varchar(20),
	"graduation_year" integer,
	"learning_style" varchar(50),
	"accommodations" text,
	"special_needs" text,
	"preferred_subjects" text,
	"difficult_subjects" text,
	"learning_goals" text,
	"content_filter_level" varchar(20) DEFAULT 'strict',
	"allowed_topics" text,
	"restricted_topics" text,
	"max_session_time_minutes" integer DEFAULT 60,
	"show_progress_to_parents" boolean DEFAULT true,
	"share_data_with_teachers" boolean DEFAULT true,
	"emergency_contact_name" varchar(255),
	"emergency_contact_phone" varchar(50),
	"emergency_contact_relation" varchar(50),
	"is_active" boolean DEFAULT true,
	"settings" jsonb,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	"created_by" uuid,
	CONSTRAINT "student_profiles_user_id_unique" UNIQUE("user_id")
);
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "activity_logs" ADD CONSTRAINT "activity_logs_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "activity_logs" ADD CONSTRAINT "activity_logs_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "content_analytics" ADD CONSTRAINT "content_analytics_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "learning_analytics" ADD CONSTRAINT "learning_analytics_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "learning_analytics" ADD CONSTRAINT "learning_analytics_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "learning_analytics" ADD CONSTRAINT "learning_analytics_classroom_id_classrooms_id_fk" FOREIGN KEY ("classroom_id") REFERENCES "public"."classrooms"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "performance_metrics" ADD CONSTRAINT "performance_metrics_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "usage_analytics" ADD CONSTRAINT "usage_analytics_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "audit_logs" ADD CONSTRAINT "audit_logs_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "audit_logs" ADD CONSTRAINT "audit_logs_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "consent_records" ADD CONSTRAINT "consent_records_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "consent_records" ADD CONSTRAINT "consent_records_student_id_users_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "consent_records" ADD CONSTRAINT "consent_records_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "consent_records" ADD CONSTRAINT "consent_records_withdrawn_by_users_id_fk" FOREIGN KEY ("withdrawn_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "data_access_requests" ADD CONSTRAINT "data_access_requests_requester_id_users_id_fk" FOREIGN KEY ("requester_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "data_access_requests" ADD CONSTRAINT "data_access_requests_student_id_users_id_fk" FOREIGN KEY ("student_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "data_access_requests" ADD CONSTRAINT "data_access_requests_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "data_access_requests" ADD CONSTRAINT "data_access_requests_assigned_to_users_id_fk" FOREIGN KEY ("assigned_to") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "data_access_requests" ADD CONSTRAINT "data_access_requests_reviewed_by_users_id_fk" FOREIGN KEY ("reviewed_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "data_retention_schedule" ADD CONSTRAINT "data_retention_schedule_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "data_retention_schedule" ADD CONSTRAINT "data_retention_schedule_hold_by_users_id_fk" FOREIGN KEY ("hold_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "data_retention_schedule" ADD CONSTRAINT "data_retention_schedule_deleted_by_users_id_fk" FOREIGN KEY ("deleted_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "security_incidents" ADD CONSTRAINT "security_incidents_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "security_incidents" ADD CONSTRAINT "security_incidents_reported_by_users_id_fk" FOREIGN KEY ("reported_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "security_incidents" ADD CONSTRAINT "security_incidents_assigned_to_users_id_fk" FOREIGN KEY ("assigned_to") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "email_verification_tokens" ADD CONSTRAINT "email_verification_tokens_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "parent_child_relations" ADD CONSTRAINT "parent_child_relations_parent_id_users_id_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "parent_child_relations" ADD CONSTRAINT "parent_child_relations_child_id_users_id_fk" FOREIGN KEY ("child_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "parent_child_relations" ADD CONSTRAINT "parent_child_relations_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "password_reset_tokens" ADD CONSTRAINT "password_reset_tokens_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "user_sessions" ADD CONSTRAINT "user_sessions_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "user_sessions" ADD CONSTRAINT "user_sessions_revoked_by_users_id_fk" FOREIGN KEY ("revoked_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "classroom_enrollments" ADD CONSTRAINT "classroom_enrollments_classroom_id_classrooms_id_fk" FOREIGN KEY ("classroom_id") REFERENCES "public"."classrooms"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "classroom_enrollments" ADD CONSTRAINT "classroom_enrollments_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "classroom_enrollments" ADD CONSTRAINT "classroom_enrollments_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "classrooms" ADD CONSTRAINT "classrooms_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "classrooms" ADD CONSTRAINT "classrooms_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "organization_invitations" ADD CONSTRAINT "organization_invitations_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "organization_invitations" ADD CONSTRAINT "organization_invitations_accepted_by_users_id_fk" FOREIGN KEY ("accepted_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "organization_invitations" ADD CONSTRAINT "organization_invitations_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "organization_memberships" ADD CONSTRAINT "organization_memberships_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "organization_memberships" ADD CONSTRAINT "organization_memberships_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "organization_memberships" ADD CONSTRAINT "organization_memberships_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "organizations" ADD CONSTRAINT "organizations_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "achievements" ADD CONSTRAINT "achievements_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "achievements" ADD CONSTRAINT "achievements_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "chat_sessions" ADD CONSTRAINT "chat_sessions_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "chat_sessions" ADD CONSTRAINT "chat_sessions_material_id_materials_id_fk" FOREIGN KEY ("material_id") REFERENCES "public"."materials"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "chat_sessions" ADD CONSTRAINT "chat_sessions_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "flip_cards" ADD CONSTRAINT "flip_cards_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "flip_cards" ADD CONSTRAINT "flip_cards_material_id_materials_id_fk" FOREIGN KEY ("material_id") REFERENCES "public"."materials"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "flip_cards" ADD CONSTRAINT "flip_cards_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "materials" ADD CONSTRAINT "materials_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "materials" ADD CONSTRAINT "materials_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "materials" ADD CONSTRAINT "materials_classroom_id_classrooms_id_fk" FOREIGN KEY ("classroom_id") REFERENCES "public"."classrooms"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "quiz_results" ADD CONSTRAINT "quiz_results_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "quiz_results" ADD CONSTRAINT "quiz_results_material_id_materials_id_fk" FOREIGN KEY ("material_id") REFERENCES "public"."materials"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "quiz_results" ADD CONSTRAINT "quiz_results_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "quiz_results" ADD CONSTRAINT "quiz_results_classroom_id_classrooms_id_fk" FOREIGN KEY ("classroom_id") REFERENCES "public"."classrooms"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "student_profiles" ADD CONSTRAINT "student_profiles_user_id_users_id_fk" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "student_profiles" ADD CONSTRAINT "student_profiles_organization_id_organizations_id_fk" FOREIGN KEY ("organization_id") REFERENCES "public"."organizations"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
DO $$ BEGIN
 ALTER TABLE "student_profiles" ADD CONSTRAINT "student_profiles_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;
EXCEPTION
 WHEN duplicate_object THEN null;
END $$;
--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "activity_logs_user_activity_idx" ON "activity_logs" USING btree ("user_id","activity_type","created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "activity_logs_org_activity_idx" ON "activity_logs" USING btree ("organization_id","activity_type","created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "activity_logs_type_time_idx" ON "activity_logs" USING btree ("activity_type","created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "content_analytics_material_date_idx" ON "content_analytics" USING btree ("material_id","analytics_date");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "content_analytics_subject_date_idx" ON "content_analytics" USING btree ("subject","analytics_date");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "learning_analytics_user_date_idx" ON "learning_analytics" USING btree ("user_id","analytics_date");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "learning_analytics_org_date_idx" ON "learning_analytics" USING btree ("organization_id","analytics_date");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "performance_metrics_metric_time_idx" ON "performance_metrics" USING btree ("metric_name","timestamp");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "performance_metrics_endpoint_time_idx" ON "performance_metrics" USING btree ("endpoint","timestamp");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "usage_analytics_date_org_idx" ON "usage_analytics" USING btree ("analytics_date","organization_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "audit_logs_user_action_idx" ON "audit_logs" USING btree ("user_id","action","created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "audit_logs_entity_idx" ON "audit_logs" USING btree ("entity_type","entity_id","created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "audit_logs_student_data_idx" ON "audit_logs" USING btree ("student_data_accessed","created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "audit_logs_org_date_idx" ON "audit_logs" USING btree ("organization_id","created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "audit_logs_risk_level_idx" ON "audit_logs" USING btree ("risk_level","created_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "consent_records_user_consent_idx" ON "consent_records" USING btree ("user_id","consent_type","status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "consent_records_student_consent_idx" ON "consent_records" USING btree ("student_id","consent_type","status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "consent_records_expiration_idx" ON "consent_records" USING btree ("expires_at","status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "data_access_requests_requester_status_idx" ON "data_access_requests" USING btree ("requester_id","status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "data_access_requests_student_status_idx" ON "data_access_requests" USING btree ("student_id","status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "data_access_requests_status_due_date_idx" ON "data_access_requests" USING btree ("status","due_date");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "data_retention_schedule_entity_idx" ON "data_retention_schedule" USING btree ("entity_type","entity_id");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "data_retention_schedule_deletion_date_idx" ON "data_retention_schedule" USING btree ("scheduled_deletion_date","status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "data_retention_schedule_org_status_idx" ON "data_retention_schedule" USING btree ("organization_id","status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "security_incidents_severity_status_idx" ON "security_incidents" USING btree ("severity","status");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "security_incidents_org_incident_idx" ON "security_incidents" USING btree ("organization_id","detected_at");--> statement-breakpoint
CREATE INDEX IF NOT EXISTS "security_incidents_student_data_idx" ON "security_incidents" USING btree ("student_data_involved","detected_at");