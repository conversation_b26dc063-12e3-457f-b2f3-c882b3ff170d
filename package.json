{"name": "nextn", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack -p 9002", "genkit:dev": "genkit start -- tsx src/ai/dev.ts", "genkit:watch": "genkit start -- tsx --watch src/ai/dev.ts", "build": "next build", "start": "next start", "lint": "next lint", "typecheck": "tsc --noEmit", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@auth/drizzle-adapter": "^1.10.0", "@auth/supabase-adapter": "^1.10.0", "@genkit-ai/googleai": "^1.14.1", "@genkit-ai/next": "^1.14.1", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-menubar": "^1.1.6", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.2", "@radix-ui/react-radio-group": "^1.2.3", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@supabase/supabase-js": "^2.45.4", "@upstash/redis": "^1.35.0", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.33.0", "embla-carousel-react": "^8.6.0", "firebase": "^11.9.1", "framer-motion": "^11.5.7", "genkit": "^1.14.1", "jose": "^5.9.6", "lucide-react": "^0.475.0", "next": "15.3.3", "next-auth": "^4.24.8", "nodemailer": "^6.10.1", "patch-package": "^8.0.0", "postgres": "^3.4.4", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-dropzone": "^14.2.3", "react-hook-form": "^7.54.2", "recharts": "^2.15.1", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2"}, "devDependencies": {"@genkit-ai/firebase": "^1.15.5", "@opentelemetry/exporter-jaeger": "^2.0.1", "@types/bcryptjs": "^2.4.6", "@types/node": "^20", "@types/nodemailer": "^6.4.17", "@types/react": "^18", "@types/react-dom": "^18", "drizzle-kit": "^0.31.4", "genkit-cli": "^0.0.2", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}