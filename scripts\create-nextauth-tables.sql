-- Create NextAuth required tables for <PERSON><PERSON><PERSON><PERSON><PERSON>pter
-- These tables are required for Next<PERSON>uth authentication to work properly

-- Users table (singular as expected by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)
CREATE TABLE IF NOT EXISTS "user" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "name" VARCHAR(255),
    "email" VARCHAR(255) UNIQUE NOT NULL,
    "emailVerified" TIMESTAMP,
    "image" TEXT
);

-- Accounts table for OAuth providers
CREATE TABLE IF NOT EXISTS "account" (
    "userId" UUID NOT NULL REFERENCES "user"(id) ON DELETE CASCADE,
    "type" VARCHAR(255) NOT NULL,
    "provider" VARCHAR(255) NOT NULL,
    "providerAccountId" VARCHAR(255) NOT NULL,
    "refresh_token" TEXT,
    "access_token" TEXT,
    "expires_at" INTEGER,
    "token_type" VARCHAR(255),
    "scope" VARCHAR(255),
    "id_token" TEXT,
    "session_state" VARC<PERSON><PERSON>(255),
    PRIMARY KEY ("provider", "providerAccountId")
);

-- Sessions table for user sessions
CREATE TABLE IF NOT EXISTS "session" (
    "sessionToken" VARCHAR(255) PRIMARY KEY NOT NULL,
    "userId" UUID NOT NULL REFERENCES "user"(id) ON DELETE CASCADE,
    "expires" TIMESTAMP NOT NULL
);

-- Verification tokens for email verification
CREATE TABLE IF NOT EXISTS "verificationToken" (
    "identifier" VARCHAR(255) NOT NULL,
    "token" VARCHAR(255) NOT NULL,
    "expires" TIMESTAMP NOT NULL,
    PRIMARY KEY ("identifier", "token")
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS "user_email_idx" ON "user"("email");
CREATE INDEX IF NOT EXISTS "account_userId_idx" ON "account"("userId");
CREATE INDEX IF NOT EXISTS "session_userId_idx" ON "session"("userId");