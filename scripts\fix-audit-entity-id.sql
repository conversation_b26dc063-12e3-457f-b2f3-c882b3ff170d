-- Fix audit_logs entity_id column to accept text values instead of only UUID
-- This allows storing email addresses and other string identifiers

-- Drop the existing index that references entity_id
DROP INDEX IF EXISTS audit_logs_entity_idx;

-- Change entity_id column from UUID to VARCHAR
ALTER TABLE audit_logs 
ALTER COLUMN entity_id TYPE VARCHAR(255) USING entity_id::text;

-- Recreate the index
CREATE INDEX audit_logs_entity_idx ON audit_logs(entity_type, entity_id, created_at);

-- Also fix similar columns in other tables that might have the same issue
-- Fix data_retention_schedule entity_id column
DROP INDEX IF EXISTS data_retention_schedule_entity_idx;
ALTER TABLE data_retention_schedule 
ALTER COLUMN entity_id TYPE VARCHAR(255) USING entity_id::text;
CREATE INDEX data_retention_schedule_entity_idx ON data_retention_schedule(entity_type, entity_id);