const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

async function runMigration() {
  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL environment variable is required');
    process.exit(1);
  }

  const client = postgres(process.env.DATABASE_URL, { prepare: false });
  
  try {
    // Read the SQL file
    const sqlFile = path.join(__dirname, 'fix-audit-entity-id.sql');
    const sql = fs.readFileSync(sqlFile, 'utf8');
    
    console.log('Fixing audit_logs entity_id column type...');
    await client.unsafe(sql);
    console.log('✅ Audit logs schema updated successfully!');
  } catch (error) {
    console.error('❌ Error updating audit logs schema:', error.message);
    process.exit(1);
  } finally {
    await client.end();
  }
}

runMigration();