const { drizzle } = require('drizzle-orm/postgres-js');
const postgres = require('postgres');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

async function runMigration() {
  if (!process.env.DATABASE_URL) {
    console.error('DATABASE_URL environment variable is required');
    process.exit(1);
  }

  const client = postgres(process.env.DATABASE_URL, { prepare: false });
  
  try {
    // Read the SQL file
    const sqlFile = path.join(__dirname, 'create-nextauth-tables.sql');
    const sql = fs.readFileSync(sqlFile, 'utf8');
    
    console.log('Creating NextAuth tables...');
    await client.unsafe(sql);
    console.log('✅ NextAuth tables created successfully!');
  } catch (error) {
    console.error('❌ Error creating NextAuth tables:', error.message);
    process.exit(1);
  } finally {
    await client.end();
  }
}

runMigration();