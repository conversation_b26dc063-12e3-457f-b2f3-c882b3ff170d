// src/ai/flows/answer-questions.ts
'use server';
/**
 * @fileOverview A flow that answers questions about a subject matter using provided context.
 *
 * - answerQuestions - A function that takes a question and context, and returns an answer.
 * - AnswerQuestionsInput - The input type for the answerQuestions function.
 * - AnswerQuestionsOutput - The return type for the answerQuestions function.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const AnswerQuestionsInputSchema = z.object({
	question: z.string().describe('The question to be answered.'),
	context: z
		.string()
		.describe('The context to use when answering the question.'),
	ageGroup: z
		.enum(['elementary', 'middle', 'high', 'adult'])
		.default('middle')
		.describe('Age group to adapt tone and complexity'),
});
export type AnswerQuestionsInput = z.infer<typeof AnswerQuestionsInputSchema>;

const AnswerQuestionsOutputSchema = z.object({
	answer: z.string().describe('The answer to the question.'),
	citation: z
		.string()
		.optional()
		.describe('Simple source citation if based on provided context'),
});
export type AnswerQuestionsOutput = z.infer<typeof AnswerQuestionsOutputSchema>;

export async function answerQuestions(
	input: AnswerQuestionsInput
): Promise<AnswerQuestionsOutput> {
	try {
		const result = await answerQuestionsFlow(input);

		// Validate output
		if (!result.answer || result.answer.trim().length === 0) {
			throw new Error('Generated answer is empty');
		}

		return result;
	} catch (error) {
		console.error('Answer generation error:', error);
		throw new Error(
			`Failed to generate answer: ${
				error instanceof Error ? error.message : 'Unknown error'
			}`
		);
	}
}

const prompt = ai.definePrompt({
	name: 'answerQuestionsPrompt',
	input: {schema: AnswerQuestionsInputSchema},
	output: {schema: AnswerQuestionsOutputSchema},
	prompt: `You are a friendly, patient educational assistant for {{ageGroup}} students.

Guidelines:
- Use age-appropriate language and adjust complexity to {{ageGroup}}.
- Be concise and clear. Use simple examples when helpful.
- If you used the provided context, add a short citation line at the end like: "According to your document".

Context:
{{context}}

Question: {{question}}

Provide a JSON response with keys: "answer" and optional "citation".
Answer:`,
});

const answerQuestionsFlow = ai.defineFlow(
	{
		name: 'answerQuestionsFlow',
		inputSchema: AnswerQuestionsInputSchema,
		outputSchema: AnswerQuestionsOutputSchema,
	},
	async (input) => {
		try {
			const {output} = await prompt(input);

			if (!output) {
				throw new Error('No output received from AI model');
			}

			return output;
		} catch (error) {
			console.error('Answer flow error:', error);
			throw error;
		}
	}
);
