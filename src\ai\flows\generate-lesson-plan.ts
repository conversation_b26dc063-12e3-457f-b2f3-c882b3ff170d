'use server';

/**
 * @fileOverview Generates a personalized lesson plan based on skill level and goals.
 *
 * - generateLessonPlan - Main function to generate a structured plan.
 * - GenerateLessonPlanInput/Output - Strongly typed inputs/outputs validated with Zod.
 */

import {ai} from '@/ai/genkit';
import {z} from 'genkit';

const GenerateLessonPlanInputSchema = z.object({
  goals: z
    .string()
    .min(1, 'Learning goals are required')
    .describe('Student learning goals in free text'),
  skillLevel: z
    .enum(['beginner', 'intermediate', 'advanced'])
    .describe('Current skill level'),
  contextMaterial: z
    .string()
    .optional()
    .default('')
    .describe('Optional learning material/context to tailor the plan'),
  timePerWeekHours: z
    .number()
    .int()
    .min(1)
    .max(40)
    .default(5)
    .describe('Estimated study time available per week'),
  durationWeeks: z
    .number()
    .int()
    .min(1)
    .max(52)
    .default(4)
    .describe('Number of weeks to plan for'),
  learningStyle: z
    .enum(['visual', 'auditory', 'reading-writing', 'kinesthetic'])
    .optional()
    .describe('Preferred learning style if known'),
  ageGroup: z
    .enum(['elementary', 'middle', 'high', 'adult'])
    .optional()
    .describe('Age group to adjust tone and activities'),
});
export type GenerateLessonPlanInput = z.infer<typeof GenerateLessonPlanInputSchema>;

const LessonSessionSchema = z.object({
  title: z.string(),
  activities: z.array(z.string()).min(1),
  estimatedTimeHours: z.number().min(0.25).max(8),
});

const LessonWeekSchema = z.object({
  week: z.number().int().min(1),
  focus: z.string(),
  sessions: z.array(LessonSessionSchema).min(1),
});

const GenerateLessonPlanOutputSchema = z.object({
  title: z.string(),
  level: z.string(),
  overview: z.string(),
  objectives: z.array(z.string()).min(3),
  prerequisites: z.array(z.string()).optional().default([]),
  schedule: z.array(LessonWeekSchema).min(1),
  resources: z.array(z.string()).optional().default([]),
  assessments: z.array(z.string()).optional().default([]),
  studyTips: z.array(z.string()).optional().default([]),
});
export type GenerateLessonPlanOutput = z.infer<
  typeof GenerateLessonPlanOutputSchema
>;

export async function generateLessonPlan(
  input: GenerateLessonPlanInput
): Promise<GenerateLessonPlanOutput> {
  try {
    const result = await generateLessonPlanFlow(input);

    // Validate and normalize with Zod
    const validated = GenerateLessonPlanOutputSchema.parse(result);

    if (!validated.schedule || validated.schedule.length === 0) {
      throw new Error('Lesson plan schedule is empty');
    }

    return validated;
  } catch (error) {
    console.error('Lesson plan generation error:', error);
    if (error instanceof z.ZodError) {
      throw new Error(
        `Invalid lesson plan format: ${error.errors
          .map((e) => e.message)
          .join(', ')}`
      );
    }
    throw new Error(
      `Failed to generate lesson plan: ${error instanceof Error ? error.message : 'Unknown error'}`
    );
  }
}

const generateLessonPlanPrompt = ai.definePrompt({
  name: 'generateLessonPlanPrompt',
  input: {schema: GenerateLessonPlanInputSchema},
  output: {schema: GenerateLessonPlanOutputSchema},
  prompt: `You are an adaptive curriculum designer creating a concise, actionable lesson plan.

Student profile:
- Skill level: {{skillLevel}}
- Time per week (hours): {{timePerWeekHours}}
- Duration (weeks): {{durationWeeks}}
- Preferred learning style: {{learningStyle}}
- Age group: {{ageGroup}}

Learning goals: {{{goals}}}

Optional context material to anchor topics (if provided):
{{{contextMaterial}}}

Requirements:
- Return a structured JSON object matching the output schema.
- Objectives should be measurable and aligned to the goals.
- Schedule must include {{durationWeeks}} weeks (or the closest reasonable number if goals demand).
- Each week should include 2-4 sessions with concrete activities and time estimates.
- Include quick assessments and resources when helpful.
- Keep the tone age-appropriate.
`,
});

const generateLessonPlanFlow = ai.defineFlow(
  {
    name: 'generateLessonPlanFlow',
    inputSchema: GenerateLessonPlanInputSchema,
    outputSchema: GenerateLessonPlanOutputSchema,
  },
  async (input) => {
    try {
      const {output} = await generateLessonPlanPrompt(input);
      if (!output) {
        throw new Error('No output received from AI model');
      }
      return output;
    } catch (error) {
      console.error('Lesson plan generation flow error:', error);
      throw error;
    }
  }
);


