import {NextRequest, NextResponse} from 'next/server';
import {db} from '@/lib/db/connection';
import {users} from '@/lib/db/schema';
import {eq} from 'drizzle-orm';
import {z} from 'zod';

const RegistrationSchema = z.object({
	firstName: z.string().min(1, 'First name is required'),
	lastName: z.string().min(1, 'Last name is required'),
	email: z.string().email('Invalid email address'),
	// Client no longer controls ageGroup; derive from DOB
	birthDate: z.string().optional(), // ISO date string
	role: z.enum(['student', 'teacher', 'parent_guardian']),
	// These are hints; server will recompute
	isMinor: z.boolean().optional(),
	parentalConsent: z.boolean().optional(),
});

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();
		const validatedData = RegistrationSchema.parse(body);

		// Check if user already exists
		const existingUser = await db
			.select()
			.from(users)
			.where(eq(users.email, validatedData.email))
			.limit(1);

		if (existingUser.length > 0) {
			return NextResponse.json(
				{
					success: false,
					error: 'User with this email already exists',
				},
				{status: 409}
			);
		}

		// Derive age and ageGroup from birthDate when provided
		let computedIsMinor = false;
		let computedAgeGroup: 'elementary' | 'middle' | 'high' | 'adult' =
			'elementary';
		if (validatedData.birthDate) {
			const dob = new Date(validatedData.birthDate);
			if (!Number.isNaN(dob.getTime())) {
				const today = new Date();
				let age = today.getFullYear() - dob.getFullYear();
				const m = today.getMonth() - dob.getMonth();
				if (m < 0 || (m === 0 && today.getDate() < dob.getDate())) age--;
				computedIsMinor = age < 13;
				if (age < 11) computedAgeGroup = 'elementary';
				else if (age < 14) computedAgeGroup = 'middle';
				else if (age < 19) computedAgeGroup = 'high';
				else computedAgeGroup = 'adult';
			}
		}

		// COPPA: if minor, require parental consent
		if (computedIsMinor) {
			const consentProvided = Boolean(validatedData.parentalConsent);
			if (!consentProvided) {
				return NextResponse.json(
					{
						success: false,
						error: 'Parental consent is required for users under 13 years old',
					},
					{status: 400}
				);
			}
		}

		// Create user record
		const birthDateStr = validatedData.birthDate
			? validatedData.birthDate.substring(0, 10)
			: null;

		const newUser = await db
			.insert(users)
			.values({
				email: validatedData.email,
				firstName: validatedData.firstName,
				lastName: validatedData.lastName,
				displayName: `${validatedData.firstName} ${validatedData.lastName}`,
				role: validatedData.role,
				birthDate: birthDateStr,
				ageGroup: computedAgeGroup,
				isMinor: computedIsMinor,
				parentalConsentGiven: computedIsMinor
					? true
					: Boolean(validatedData.parentalConsent),
				parentalConsentDate:
					computedIsMinor && validatedData.parentalConsent ? new Date() : null,
				contentFilterLevel: 'strict',
				isActive: true,
				createdAt: new Date(),
				updatedAt: new Date(),
			})
			.returning();

		return NextResponse.json({
			success: true,
			message: 'User registered successfully',
			user: {
				id: newUser[0].id,
				email: newUser[0].email,
				firstName: newUser[0].firstName,
				lastName: newUser[0].lastName,
				role: newUser[0].role,
				ageGroup: newUser[0].ageGroup,
			},
		});
	} catch (error) {
		console.error('Registration error:', error);

		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					success: false,
					error: 'Validation error',
					details: error.errors,
				},
				{status: 400}
			);
		}

		return NextResponse.json(
			{
				success: false,
				error: 'Internal server error',
			},
			{status: 500}
		);
	}
}
