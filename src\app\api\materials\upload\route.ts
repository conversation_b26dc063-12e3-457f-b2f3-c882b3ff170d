import {NextRequest, NextResponse} from 'next/server';
import {requireApiSession} from '@/lib/auth/session';
import {db} from '@/lib/db/connection';
import {materials} from '@/lib/db/schema';
import {contentSafetyFilter, ContentSafetyError} from '@/lib/content-safety';
import {z} from 'zod';

const UploadSchema = z.object({
	name: z.string().min(1),
	content: z.string().min(1),
});

export async function POST(request: NextRequest) {
	const auth = await requireApiSession();
	if (!auth.ok) return auth.response;

	try {
		const body = await request.json();
		const {name, content} = UploadSchema.parse(body);

		const ageGroupFromSession = auth.session.user.ageGroup as
			| 'elementary'
			| 'middle'
			| 'high'
			| 'adult';

		// Safety check content prior to persisting
		contentSafetyFilter.updateConfig({
			strictMode: true,
			ageGroup: ageGroupFromSession,
			educationalContext: true,
		});
		const safety = await contentSafetyFilter.checkContent(content);
		if (!safety.isAppropriate) {
			throw new ContentSafetyError(
				'Uploaded content failed safety checks',
				safety.violations,
				safety.riskLevel
			);
		}

		const inserted = await db
			.insert(materials)
			.values({
				userId: auth.session.user.id,
				name,
				type: 'text',
				content,
				safetyStatus: 'approved',
				safetyScore: String(Math.round((safety.confidence ?? 0.9) * 100)),
				ageGroup: ageGroupFromSession,
				isPublic: false,
			})
			.returning();

		return NextResponse.json({success: true, data: {id: inserted[0].id}});
	} catch (error) {
		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{success: false, error: 'Invalid data', details: error.errors},
				{status: 400}
			);
		}
		if (error instanceof ContentSafetyError) {
			return NextResponse.json(
				{
					success: false,
					error: error.message,
					violations: error.violations,
					riskLevel: error.riskLevel,
				},
				{status: 400}
			);
		}
		return NextResponse.json(
			{success: false, error: 'Failed to upload material'},
			{status: 500}
		);
	}
}
