import {NextRequest, NextResponse} from 'next/server';
import {requireApiSession} from '@/lib/auth/session';
import {
	answerQuestions,
	AnswerQuestionsInput,
} from '@/ai/flows/answer-questions';
import {validateAIResponse} from '@/lib/ai-response-validator';
import {contentSafetyFilter, ContentSafetyError} from '@/lib/content-safety';
import {z} from 'zod';

const RequestSchema = z.object({
	question: z.string().min(1, 'Question is required'),
	context: z.string().min(1, 'Context is required'),
});

export async function POST(request: NextRequest) {
	const auth = await requireApiSession();
	if (!auth.ok) return auth.response;

	let validatedInput: z.infer<typeof RequestSchema> | null = null;

	try {
		const body = await request.json();

		// Validate input schema
		validatedInput = RequestSchema.parse(body);

		// Derive age group from session, not client input
		const ageGroupFromSession = auth.session.user.ageGroup as
			| 'elementary'
			| 'middle'
			| 'high'
			| 'adult';

		// Step 1: Content safety check on question and context
		contentSafetyFilter.updateConfig({
			strictMode: false, // Made less strict for educational content
			ageGroup: ageGroupFromSession,
			educationalContext: true,
			debugMode: true, // Enable debug mode to see what's happening
		});

		const questionSafetyResult = await contentSafetyFilter.checkContent(
			validatedInput.question
		);
		if (!questionSafetyResult.isAppropriate) {
			throw new ContentSafetyError(
				'Question contains inappropriate content',
				questionSafetyResult.violations,
				questionSafetyResult.riskLevel
			);
		}

		const contextSafetyResult = await contentSafetyFilter.checkContent(
			validatedInput.context
		);
		if (!contextSafetyResult.isAppropriate) {
			throw new ContentSafetyError(
				'Context material contains inappropriate content',
				contextSafetyResult.violations,
				contextSafetyResult.riskLevel
			);
		}

		// Step 2: Answer question using AI flow
		const result = await answerQuestions({
			question: validatedInput.question,
			context: validatedInput.context,
			ageGroup: ageGroupFromSession,
		});

		// Step 3: Validate answer is not empty
		if (!result.answer || result.answer.trim().length === 0) {
			throw new Error('Empty answer generated');
		}

		// Step 4: Validate AI response for safety and educational value
		const outputValidation = await validateAIResponse(
			result.answer,
			'general',
			ageGroupFromSession
		);

		// Log debug information
		console.log('AI Response Validation Debug:', {
			answer: result.answer.substring(0, 200) + '...',
			validation: outputValidation,
			ageGroup: ageGroupFromSession,
		});

		if (!outputValidation.isValid) {
			console.warn(
				'Question answer failed safety validation:',
				outputValidation.issues
			);

			// For critical safety issues, block the response
			if (
				outputValidation.issues.some(
					(issue) => issue.includes('inappropriate') || issue.includes('safety')
				)
			) {
				throw new ContentSafetyError(
					'Generated answer contains inappropriate content',
					outputValidation.issues,
					'high'
				);
			}
		}

		return NextResponse.json({
			success: true,
			data: result,
			safetyInfo: {
				questionSafetyScore: questionSafetyResult.confidence,
				contextSafetyScore: contextSafetyResult.confidence,
				outputSafetyScore: outputValidation.safetyScore,
				educationalValue: outputValidation.educationalValue,
				ageAppropriate: outputValidation.ageAppropriate,
			},
		});
	} catch (error) {
		console.error('Question answering error:', error);

		if (error instanceof ContentSafetyError) {
			const ageGroupFromSession = auth.ok
				? (auth.session.user.ageGroup as
						| 'elementary'
						| 'middle'
						| 'high'
						| 'adult')
				: 'middle';
			return NextResponse.json(
				{
					success: false,
					error: error.message,
					violations: error.violations,
					riskLevel: error.riskLevel,
					safetyInfo: {
						message:
							'Content did not meet safety guidelines for educational use',
						ageGroup: ageGroupFromSession,
					},
				},
				{status: 400}
			);
		}

		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					success: false,
					error: 'Invalid input data',
					details: error.errors,
				},
				{status: 400}
			);
		}

		return NextResponse.json(
			{
				success: false,
				error: 'Failed to answer question',
			},
			{status: 500}
		);
	}
}
