import {NextRequest, NextResponse} from 'next/server';
import {requireApiSession} from '@/lib/auth/session';

export async function GET(request: NextRequest) {
	const auth = await requireApiSession();

	if (!auth.ok) {
		return auth.response;
	}

	return NextResponse.json({
		success: true,
		message: 'Authentication successful',
		user: {
			id: auth.session.user.id,
			email: auth.session.user.email,
			role: auth.session.user.role,
			ageGroup: auth.session.user.ageGroup,
		},
	});
}
