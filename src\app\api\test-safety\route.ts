import {NextRequest, NextResponse} from 'next/server';
import {requireApiSession} from '@/lib/auth/session';
import {debugContentSafety} from '@/lib/content-safety';

export async function POST(request: NextRequest) {
	const auth = await requireApiSession();
	if (!auth.ok) return auth.response;
	try {
		const body = await request.json();
		const {text, ageGroup = 'middle'} = body;

		if (!text) {
			return NextResponse.json(
				{
					success: false,
					error: 'Text is required',
				},
				{status: 400}
			);
		}

		// Test the content safety filter with debug mode
		const result = await debugContentSafety(text, {
			ageGroup,
			educationalContext: true,
			debugMode: true,
		});

		return NextResponse.json({
			success: true,
			text,
			ageGroup,
			result,
		});
	} catch (error) {
		console.error('Test safety error:', error);
		return NextResponse.json(
			{
				success: false,
				error: 'Failed to test content safety',
			},
			{status: 500}
		);
	}
}
