'use client';

import {useSearchParams} from 'next/navigation';
import {Suspense} from 'react';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {Button} from '@/components/ui/button';
import {BrainCircuit, AlertTriangle} from 'lucide-react';
import Link from 'next/link';

function AuthErrorContent() {
	const searchParams = useSearchParams();
	const error = searchParams.get('error');

	const getErrorDetails = (errorCode: string | null) => {
		switch (errorCode) {
			case 'Configuration':
				return {
					title: 'Server Error',
					description: 'There is a problem with the server configuration.',
				};
			case 'AccessDenied':
				return {
					title: 'Access Denied',
					description: 'You do not have permission to sign in.',
				};
			case 'Verification':
				return {
					title: 'Verification Failed',
					description: 'The verification link is invalid or has expired.',
				};
			case 'Default':
			default:
				return {
					title: 'Authentication Error',
					description:
						'An error occurred during authentication. Please try again.',
				};
		}
	};

	const errorDetails = getErrorDetails(error);

	return (
		<Card className='w-full max-w-sm shadow-2xl'>
			<CardHeader className='text-center'>
				<div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-destructive'>
					<AlertTriangle className='h-8 w-8 text-destructive-foreground' />
				</div>
				<CardTitle className='text-xl text-destructive'>
					{errorDetails.title}
				</CardTitle>
				<CardDescription>{errorDetails.description}</CardDescription>
			</CardHeader>
			<CardContent className='text-center space-y-4'>
				<p className='text-sm text-muted-foreground'>
					If you continue to experience issues, please contact support.
				</p>

				<div className='pt-4 space-y-2'>
					<Link href='/auth/signin'>
						<Button className='w-full'>Try Again</Button>
					</Link>
					<Link href='/'>
						<Button variant='outline' className='w-full'>
							Go Home
						</Button>
					</Link>
				</div>
			</CardContent>
		</Card>
	);
}

export default function AuthErrorPage() {
	return (
		<main className='flex min-h-screen flex-col items-center justify-center p-4'>
			<Suspense fallback={
				<Card className='w-full max-w-sm shadow-2xl'>
					<CardHeader className='text-center'>
						<div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-destructive'>
							<AlertTriangle className='h-8 w-8 text-destructive-foreground' />
						</div>
						<CardTitle className='text-xl text-destructive'>
							Loading...
						</CardTitle>
						<CardDescription>Loading error details...</CardDescription>
					</CardHeader>
					<CardContent className='text-center space-y-4'>
						<div className='pt-4 space-y-2'>
							<Button className='w-full' disabled>Loading...</Button>
							<Link href='/'>
								<Button variant='outline' className='w-full'>
									Go Home
								</Button>
							</Link>
						</div>
					</CardContent>
				</Card>
			}>
				<AuthErrorContent />
			</Suspense>
		</main>
	);
}
