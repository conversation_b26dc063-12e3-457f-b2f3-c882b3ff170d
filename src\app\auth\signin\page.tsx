'use client';

import {useState, Suspense} from 'react';
import {signIn, getSession} from 'next-auth/react';
import {useRouter, useSearchParams} from 'next/navigation';
import {Button} from '@/components/ui/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {Input} from '@/components/ui/input';
import {Label} from '@/components/ui/label';
import {BrainCircuit, Mail, Chrome} from 'lucide-react';
import {useToast} from '@/hooks/use-toast';
import Link from 'next/link';

function SignInContent() {
	const router = useRouter();
	const searchParams = useSearchParams();
	const {toast} = useToast();
	const [email, setEmail] = useState('');
	const [isLoading, setIsLoading] = useState(false);
	const [isEmailSent, setIsEmailSent] = useState(false);

	const callbackUrl = searchParams.get('callbackUrl') || '/';

	const handleEmailSignIn = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!email) {
			toast({
				title: 'Email Required',
				description: 'Please enter your email address.',
				variant: 'destructive',
			});
			return;
		}

		setIsLoading(true);
		try {
			const result = await signIn('email', {
				email,
				callbackUrl,
				redirect: false,
			});

			if (result?.error) {
				toast({
					title: 'Sign In Failed',
					description: result.error,
					variant: 'destructive',
				});
			} else {
				setIsEmailSent(true);
				toast({
					title: 'Check Your Email',
					description: 'We sent you a sign-in link. Please check your email.',
				});
			}
		} catch (error) {
			toast({
				title: 'Sign In Failed',
				description: 'An error occurred. Please try again.',
				variant: 'destructive',
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleGoogleSignIn = async () => {
		setIsLoading(true);
		try {
			await signIn('google', {callbackUrl});
		} catch (error) {
			toast({
				title: 'Sign In Failed',
				description: 'An error occurred. Please try again.',
				variant: 'destructive',
			});
			setIsLoading(false);
		}
	};

	if (isEmailSent) {
		return (
			<Card className='w-full max-w-sm shadow-2xl'>
				<CardHeader className='text-center'>
					<div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary'>
						<Mail className='h-8 w-8 text-primary-foreground' />
					</div>
					<CardTitle className='text-xl'>Check Your Email</CardTitle>
					<CardDescription>
						We sent a sign-in link to <strong>{email}</strong>
					</CardDescription>
				</CardHeader>
				<CardContent className='text-center'>
					<p className='text-sm text-muted-foreground mb-4'>
						Click the link in your email to sign in to ScholarAI.
					</p>
					<Button
						variant='outline'
						onClick={() => setIsEmailSent(false)}
						className='w-full'>
						Try a different email
					</Button>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card className='w-full max-w-sm shadow-2xl'>
			<CardHeader className='text-center'>
				<div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary'>
					<BrainCircuit className='h-8 w-8 text-primary-foreground' />
				</div>
				<CardTitle className='font-headline text-3xl font-bold text-primary'>
					ScholarAI
				</CardTitle>
				<CardDescription>
					Sign in to access your learning assistant
				</CardDescription>
			</CardHeader>
			<CardContent className='space-y-4'>
				<form onSubmit={handleEmailSignIn} className='space-y-4'>
					<div className='space-y-2'>
						<Label htmlFor='email'>Email</Label>
						<Input
							id='email'
							type='email'
							placeholder='<EMAIL>'
							value={email}
							onChange={(e) => setEmail(e.target.value)}
							required
							disabled={isLoading}
						/>
					</div>
					<Button type='submit' className='w-full' disabled={isLoading}>
						{isLoading ? 'Sending...' : 'Sign in with Email'}
					</Button>
				</form>

				<div className='relative'>
					<div className='absolute inset-0 flex items-center'>
						<span className='w-full border-t' />
					</div>
					<div className='relative flex justify-center text-xs uppercase'>
						<span className='bg-background px-2 text-muted-foreground'>
							Or continue with
						</span>
					</div>
				</div>

				<Button
					variant='outline'
					onClick={handleGoogleSignIn}
					disabled={isLoading}
					className='w-full'>
					<Chrome className='mr-2 h-4 w-4' />
					Sign in with Google
				</Button>
			</CardContent>
			<CardFooter className='flex flex-col items-center space-y-2'>
				<p className='text-xs text-muted-foreground'>
					By signing in, you agree to our Terms of Service and Privacy Policy
				</p>
				<p className='text-sm text-muted-foreground'>
					Don't have an account?{' '}
					<Link href='/auth/signup' className='text-primary hover:underline'>
						Sign up
					</Link>
				</p>
			</CardFooter>
		</Card>
	);
}

export default function SignInPage() {
	return (
		<main className='flex min-h-screen flex-col items-center justify-center p-4'>
			<Suspense fallback={
				<Card className='w-full max-w-sm shadow-2xl'>
					<CardHeader className='text-center'>
						<div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary'>
							<BrainCircuit className='h-8 w-8 text-primary-foreground' />
						</div>
						<CardTitle className='font-headline text-3xl font-bold text-primary'>
							ScholarAI
						</CardTitle>
						<CardDescription>
							Loading...
						</CardDescription>
					</CardHeader>
					<CardContent className='space-y-4'>
						<div className='space-y-2'>
							<Label htmlFor='email'>Email</Label>
							<Input
								id='email'
								type='email'
								placeholder='<EMAIL>'
								disabled
							/>
						</div>
						<Button className='w-full' disabled>Loading...</Button>
					</CardContent>
					<CardFooter className='flex flex-col items-center space-y-2'>
						<p className='text-sm text-muted-foreground'>
							Don't have an account?{' '}
							<Link href='/auth/signup' className='text-primary hover:underline'>
								Sign up
							</Link>
						</p>
					</CardFooter>
				</Card>
			}>
				<SignInContent />
			</Suspense>
		</main>
	);
}
