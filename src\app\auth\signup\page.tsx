'use client';

import {useState, Suspense} from 'react';
import {signIn, getSession} from 'next-auth/react';
import {useRouter, useSearchParams} from 'next/navigation';
import {Button} from '@/components/ui/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {Input} from '@/components/ui/input';
import {Label} from '@/components/ui/label';
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from '@/components/ui/select';
import {Checkbox} from '@/components/ui/checkbox';
import {BrainCircuit, Mail, Chrome, User, Calendar} from 'lucide-react';
import {useToast} from '@/hooks/use-toast';
import Link from 'next/link';

interface RegistrationForm {
	firstName: string;
	lastName: string;
	email: string;
	birthDate?: string;
	role: 'student' | 'teacher' | 'parent_guardian';
	isMinor: boolean; // derived client-side for UX only
	parentalConsent: boolean; // only required if under 13
	termsAccepted: boolean;
}

function SignUpContent() {
	const router = useRouter();
	const searchParams = useSearchParams();
	const {toast} = useToast();
	const [isLoading, setIsLoading] = useState(false);
	const [formData, setFormData] = useState<RegistrationForm>({
		firstName: '',
		lastName: '',
		email: '',
		birthDate: '',
		role: 'student',
		isMinor: false,
		parentalConsent: false,
		termsAccepted: false,
	});

	const callbackUrl = searchParams.get('callbackUrl') || '/';

	const handleInputChange = (field: keyof RegistrationForm, value: any) => {
		setFormData((prev) => ({
			...prev,
			[field]: value,
		}));
	};

	const validateForm = (): string[] => {
		const errors: string[] = [];

		if (!formData.firstName.trim()) errors.push('First name is required');
		if (!formData.lastName.trim()) errors.push('Last name is required');
		if (!formData.email.trim()) errors.push('Email is required');
		if (!formData.email.includes('@'))
			errors.push('Please enter a valid email');
		if (!formData.termsAccepted)
			errors.push('You must accept the terms and conditions');

		// COPPA compliance checks (client-side hint; server re-validates)
		if (formData.isMinor && !formData.parentalConsent) {
			errors.push('Parental consent is required for users under 13 years old');
		}

		return errors;
	};

	const handleEmailSignUp = async (e: React.FormEvent) => {
		e.preventDefault();

		const errors = validateForm();
		if (errors.length > 0) {
			toast({
				title: 'Validation Error',
				description: errors.join(', '),
				variant: 'destructive',
			});
			return;
		}

		setIsLoading(true);
		try {
			// First, register the user with profile information
			const registrationResponse = await fetch('/api/auth/register', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					firstName: formData.firstName,
					lastName: formData.lastName,
					email: formData.email,
					birthDate: formData.birthDate,
					role: formData.role,
					parentalConsent: formData.parentalConsent,
				}),
			});

			const registrationResult = await registrationResponse.json();

			if (!registrationResponse.ok) {
				toast({
					title: 'Registration Failed',
					description: registrationResult.error || 'Failed to create account',
					variant: 'destructive',
				});
				return;
			}

			// Then, send the email sign-in link
			const result = await signIn('email', {
				email: formData.email,
				callbackUrl,
				redirect: false,
			});

			if (result?.error) {
				toast({
					title: 'Registration Failed',
					description: result.error,
					variant: 'destructive',
				});
			} else {
				toast({
					title: 'Account Created Successfully!',
					description:
						'We sent you a sign-in link. Please check your email to complete registration.',
				});
				// Redirect to verify request page
				router.push('/auth/verify-request');
			}
		} catch (error) {
			toast({
				title: 'Registration Failed',
				description: 'An error occurred. Please try again.',
				variant: 'destructive',
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleGoogleSignUp = async () => {
		const errors = validateForm();
		if (errors.length > 0) {
			toast({
				title: 'Validation Error',
				description: errors.join(', '),
				variant: 'destructive',
			});
			return;
		}

		setIsLoading(true);
		try {
			await signIn('google', {callbackUrl});
		} catch (error) {
			toast({
				title: 'Registration Failed',
				description: 'An error occurred. Please try again.',
				variant: 'destructive',
			});
			setIsLoading(false);
		}
	};

	return (
		<Card className='w-full max-w-md shadow-2xl'>
			<CardHeader className='text-center'>
				<div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary'>
					<BrainCircuit className='h-8 w-8 text-primary-foreground' />
				</div>
				<CardTitle className='font-headline text-3xl font-bold text-primary'>
					Join ScholarAI
				</CardTitle>
				<CardDescription>
					Create your account to start learning with AI
				</CardDescription>
			</CardHeader>
			<CardContent className='space-y-4'>
				<form onSubmit={handleEmailSignUp} className='space-y-4'>
					{/* Name Fields */}
					<div className='grid grid-cols-2 gap-4'>
						<div className='space-y-2'>
							<Label htmlFor='firstName'>First Name</Label>
							<Input
								id='firstName'
								value={formData.firstName}
								onChange={(e) => handleInputChange('firstName', e.target.value)}
								required
								disabled={isLoading}
							/>
						</div>
						<div className='space-y-2'>
							<Label htmlFor='lastName'>Last Name</Label>
							<Input
								id='lastName'
								value={formData.lastName}
								onChange={(e) => handleInputChange('lastName', e.target.value)}
								required
								disabled={isLoading}
							/>
						</div>
					</div>

					{/* Email Field */}
					<div className='space-y-2'>
						<Label htmlFor='email'>Email</Label>
						<Input
							id='email'
							type='email'
							placeholder='<EMAIL>'
							value={formData.email}
							onChange={(e) => handleInputChange('email', e.target.value)}
							required
							disabled={isLoading}
						/>
					</div>

					{/* Role Selection */}
					<div className='space-y-2'>
						<Label htmlFor='role'>I am a</Label>
						<Select
							value={formData.role}
							onValueChange={(value) => handleInputChange('role', value)}
							disabled={isLoading}>
							<SelectTrigger>
								<SelectValue placeholder='Select your role' />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value='student'>Student</SelectItem>
								<SelectItem value='teacher'>Teacher</SelectItem>
								<SelectItem value='parent_guardian'>Parent/Guardian</SelectItem>
							</SelectContent>
						</Select>
					</div>

					{/* Age Group Selection */}
					<div className='space-y-2'>
						<Label htmlFor='birthDate'>Date of Birth</Label>
						<Input
							id='birthDate'
							type='date'
							value={formData.birthDate}
							onChange={(e) => {
								const value = e.target.value;
								handleInputChange('birthDate', value);
								// derive minor flag client-side for UX messaging
								if (value) {
									const dob = new Date(value);
									const today = new Date();
									let age = today.getFullYear() - dob.getFullYear();
									const m = today.getMonth() - dob.getMonth();
									if (m < 0 || (m === 0 && today.getDate() < dob.getDate()))
										age--;
									handleInputChange('isMinor', age < 13);
								}
							}}
							disabled={isLoading}
						/>
					</div>

					{/* FERPA Compliance Fields */}
					<div className='space-y-3'>
						{formData.isMinor && (
							<div className='flex items-center space-x-2'>
								<Checkbox
									id='parentalConsent'
									checked={formData.parentalConsent}
									onCheckedChange={(checked) =>
										handleInputChange('parentalConsent', checked)
									}
									disabled={isLoading}
								/>
								<Label htmlFor='parentalConsent' className='text-sm'>
									I have parental consent to use this platform
								</Label>
							</div>
						)}

						<div className='flex items-center space-x-2'>
							<Checkbox
								id='termsAccepted'
								checked={formData.termsAccepted}
								onCheckedChange={(checked) =>
									handleInputChange('termsAccepted', checked)
								}
								disabled={isLoading}
							/>
							<Label htmlFor='termsAccepted' className='text-sm'>
								I agree to the{' '}
								<Link href='/terms' className='text-primary hover:underline'>
									Terms of Service
								</Link>{' '}
								and{' '}
								<Link href='/privacy' className='text-primary hover:underline'>
									Privacy Policy
								</Link>
							</Label>
						</div>
					</div>

					<Button type='submit' className='w-full' disabled={isLoading}>
						{isLoading ? 'Creating Account...' : 'Create Account with Email'}
					</Button>
				</form>

				<div className='relative'>
					<div className='absolute inset-0 flex items-center'>
						<span className='w-full border-t' />
					</div>
					<div className='relative flex justify-center text-xs uppercase'>
						<span className='bg-background px-2 text-muted-foreground'>
							Or continue with
						</span>
					</div>
				</div>

				<Button
					variant='outline'
					onClick={handleGoogleSignUp}
					disabled={isLoading}
					className='w-full'>
					<Chrome className='mr-2 h-4 w-4' />
					Sign up with Google
				</Button>
			</CardContent>
			<CardFooter className='flex justify-center'>
				<p className='text-sm text-muted-foreground'>
					Already have an account?{' '}
					<Link href='/auth/signin' className='text-primary hover:underline'>
						Sign in
					</Link>
				</p>
			</CardFooter>
		</Card>
	);
}

export default function SignUpPage() {
	return (
		<main className='flex min-h-screen flex-col items-center justify-center p-4'>
			<Suspense
				fallback={
					<Card className='w-full max-w-md shadow-2xl'>
						<CardHeader className='text-center'>
							<div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary'>
								<BrainCircuit className='h-8 w-8 text-primary-foreground' />
							</div>
							<CardTitle className='font-headline text-3xl font-bold text-primary'>
								Join ScholarAI
							</CardTitle>
							<CardDescription>Loading...</CardDescription>
						</CardHeader>
						<CardContent className='space-y-4'>
							<div className='space-y-4'>
								<div className='grid grid-cols-2 gap-4'>
									<div className='space-y-2'>
										<Label htmlFor='firstName'>First Name</Label>
										<Input id='firstName' disabled />
									</div>
									<div className='space-y-2'>
										<Label htmlFor='lastName'>Last Name</Label>
										<Input id='lastName' disabled />
									</div>
								</div>
								<div className='space-y-2'>
									<Label htmlFor='email'>Email</Label>
									<Input id='email' type='email' disabled />
								</div>
								<Button className='w-full' disabled>
									Loading...
								</Button>
							</div>
						</CardContent>
						<CardFooter className='flex justify-center'>
							<p className='text-sm text-muted-foreground'>
								Already have an account?{' '}
								<Link
									href='/auth/signin'
									className='text-primary hover:underline'>
									Sign in
								</Link>
							</p>
						</CardFooter>
					</Card>
				}>
				<SignUpContent />
			</Suspense>
		</main>
	);
}
