'use client';

import {
	<PERSON>,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {Button} from '@/components/ui/button';
import {BrainCircuit, Mail, CheckCircle} from 'lucide-react';
import Link from 'next/link';

export default function VerifyRequestPage() {
	return (
		<main className='flex min-h-screen flex-col items-center justify-center p-4'>
			<Card className='w-full max-w-sm shadow-2xl'>
				<CardHeader className='text-center'>
					<div className='mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-primary'>
						<Mail className='h-8 w-8 text-primary-foreground' />
					</div>
					<CardTitle className='text-xl'>Check Your Email</CardTitle>
					<CardDescription>
						A sign-in link has been sent to your email address
					</CardDescription>
				</CardHeader>
				<CardContent className='text-center space-y-4'>
					<div className='flex items-center justify-center space-x-2 text-sm text-muted-foreground'>
						<CheckCircle className='h-4 w-4 text-green-500' />
						<span>Email sent successfully</span>
					</div>

					<p className='text-sm text-muted-foreground'>
						If you don't see it, check your spam folder. The link will expire in
						24 hours.
					</p>

					<div className='pt-4'>
						<Link href='/auth/signin'>
							<Button variant='outline' className='w-full'>
								Back to Sign In
							</Button>
						</Link>
					</div>
				</CardContent>
			</Card>
		</main>
	);
}


