'use client';

import {useSession} from 'next-auth/react';
import {Button} from '@/components/ui/button';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import AuthStatus from '@/components/auth-status';
import {useToast} from '@/hooks/use-toast';
import {Loader2} from 'lucide-react';

export default function DebugPage() {
	const {data: session, status} = useSession();
	const {toast} = useToast();

	const testAuthAPI = async () => {
		try {
			const response = await fetch('/api/test-auth');
			const data = await response.json();

			if (data.success) {
				toast({
					title: 'API Test Successful',
					description: `Authenticated as ${data.user.email}`,
				});
			} else {
				toast({
					title: 'API Test Failed',
					description: data.error || 'Unknown error',
					variant: 'destructive',
				});
			}
		} catch (error) {
			toast({
				title: 'API Test Error',
				description: 'Failed to test authentication API',
				variant: 'destructive',
			});
		}
	};

	if (status === 'loading') {
		return (
			<div className='flex min-h-screen items-center justify-center'>
				<div className='text-center'>
					<Loader2 className='h-12 w-12 animate-spin text-primary mx-auto mb-4' />
					<p className='text-muted-foreground'>Loading...</p>
				</div>
			</div>
		);
	}

	return (
		<div className='container mx-auto p-8 space-y-6'>
			<Card>
				<CardHeader>
					<CardTitle>Authentication Debug</CardTitle>
				</CardHeader>
				<CardContent className='space-y-4'>
					<AuthStatus />

					{session && (
						<div className='space-y-2'>
							<Button onClick={testAuthAPI}>Test Authentication API</Button>
							<p className='text-sm text-muted-foreground'>
								Click the button above to test if the authentication API is
								working properly.
							</p>
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
