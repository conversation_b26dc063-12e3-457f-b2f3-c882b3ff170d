@tailwind base;
@tailwind components;
@tailwind utilities;

body {
	font-family: Arial, Helvetica, sans-serif;
}

@layer base {
	:root {
		--background: 218 93% 95%;
		--foreground: 224 71.4% 4.1%;
		--card: 220 13% 96%;
		--card-foreground: 224 71.4% 4.1%;
		--popover: 220 13% 96%;
		--popover-foreground: 224 71.4% 4.1%;
		--primary: 217 89% 61%;
		--primary-foreground: 210 20% 98%;
		--secondary: 220 14.3% 95.9%;
		--secondary-foreground: 220.9 39.3% 11%;
		--muted: 220 14.3% 95.9%;
		--muted-foreground: 220 8.9% 46.1%;
		--accent: 187 100% 42%;
		--accent-foreground: 0 0% 100%;
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 0 0% 98%;
		--border: 220 13% 91%;
		--input: 220 13% 91%;
		--ring: 217 89% 61%;
		--chart-1: 12 76% 61%;
		--chart-2: 173 58% 39%;
		--chart-3: 197 37% 24%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;
		--radius: 0.5rem;
		--sidebar-background: 0 0% 98%;
		--sidebar-foreground: 240 5.3% 26.1%;
		--sidebar-primary: 240 5.9% 10%;
		--sidebar-primary-foreground: 0 0% 98%;
		--sidebar-accent: 240 4.8% 95.9%;
		--sidebar-accent-foreground: 240 5.9% 10%;
		--sidebar-border: 220 13% 91%;
		--sidebar-ring: 217.2 91.2% 59.8%;
	}
	.dark {
		--background: 224 71.4% 4.1%;
		--foreground: 210 20% 98%;
		--card: 224 71.4% 4.1%;
		--card-foreground: 210 20% 98%;
		--popover: 224 71.4% 4.1%;
		--popover-foreground: 210 20% 98%;
		--primary: 210 20% 98%;
		--primary-foreground: 220.9 39.3% 11%;
		--secondary: 215 27.9% 16.9%;
		--secondary-foreground: 210 20% 98%;
		--muted: 215 27.9% 16.9%;
		--muted-foreground: 217.9 10.6% 64.9%;
		--accent: 217 89% 61%;
		--accent-foreground: 210 20% 98%;
		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 0 0% 98%;
		--border: 215 27.9% 16.9%;
		--input: 215 27.9% 16.9%;
		--ring: 217 89% 61%;
		--chart-1: 220 70% 50%;
		--chart-2: 160 60% 45%;
		--chart-3: 30 80% 55%;
		--chart-4: 280 65% 60%;
		--chart-5: 340 75% 55%;
		--sidebar-background: 240 5.9% 10%;
		--sidebar-foreground: 240 4.8% 95.9%;
		--sidebar-primary: 224.3 76.3% 48%;
		--sidebar-primary-foreground: 0 0% 100%;
		--sidebar-accent: 240 3.7% 15.9%;
		--sidebar-accent-foreground: 240 4.8% 95.9%;
		--sidebar-border: 240 3.7% 15.9%;
		--sidebar-ring: 217.2 91.2% 59.8%;
	}
}

@layer base {
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}
}

/* Flip card styles */
.flip-card {
	perspective: 1000px;
}
.flip-card-inner {
	position: relative;
	width: 100%;
	height: 100%;
	text-align: center;
	transition: transform 0.6s;
	transform-style: preserve-3d;
}
.flip-card.flipped .flip-card-inner {
	transform: rotateY(180deg);
}
.flip-card-front,
.flip-card-back {
	position: absolute;
	width: 100%;
	height: 100%;
	-webkit-backface-visibility: hidden;
	backface-visibility: hidden;
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 1.5rem;
	border-radius: var(--radius);
}
.flip-card-back {
	transform: rotateY(180deg);
}

/* Fun animations and vibrant styles */
@keyframes bounce-in {
	0% {
		transform: scale(0.3);
		opacity: 0;
	}
	50% {
		transform: scale(1.05);
	}
	70% {
		transform: scale(0.9);
	}
	100% {
		transform: scale(1);
		opacity: 1;
	}
}

@keyframes pulse-glow {
	0%,
	100% {
		box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
	}
	50% {
		box-shadow: 0 0 30px rgba(59, 130, 246, 0.6);
	}
}

@keyframes shake-celebrate {
	0%,
	50%,
	100% {
		transform: rotate(0deg);
	}
	10%,
	30% {
		transform: rotate(-10deg);
	}
	20%,
	40% {
		transform: rotate(10deg);
	}
}

@keyframes rainbow-border {
	0% {
		border-color: #ff6b6b;
	}
	16% {
		border-color: #4ecdc4;
	}
	33% {
		border-color: #45b7d1;
	}
	50% {
		border-color: #96ceb4;
	}
	66% {
		border-color: #ffeaa7;
	}
	83% {
		border-color: #dda0dd;
	}
	100% {
		border-color: #ff6b6b;
	}
}

.achievement-unlock {
	animation: bounce-in 0.6s ease-out;
}

.quiz-complete-glow {
	animation: pulse-glow 2s infinite;
}

.celebrate-shake {
	animation: shake-celebrate 0.5s ease-in-out;
}

.rainbow-border {
	animation: rainbow-border 3s linear infinite;
	border-width: 2px;
}

/* Gradient backgrounds for cards */
.gradient-blue {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-green {
	background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.gradient-purple {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-orange {
	background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-pink {
	background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

/* Fun hover effects */
.hover-bounce {
	transition: transform 0.2s ease-in-out;
}

.hover-bounce:hover {
	transform: translateY(-4px);
}

.hover-glow {
	transition: all 0.3s ease;
}

.hover-glow:hover {
	box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
	transform: translateY(-2px);
}

/* Progress bar enhancements */
.progress-bar-animated {
	background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
	animation: progress-shine 2s infinite;
}

@keyframes progress-shine {
	0% {
		background-position: -200px 0;
	}
	100% {
		background-position: calc(200px + 100%) 0;
	}
}

/* Badge styles */
.badge-gold {
	background: linear-gradient(135deg, #ffd700, #ffb347);
	color: #8b4513;
	border: 2px solid #ffa500;
}

.badge-silver {
	background: linear-gradient(135deg, #c0c0c0, #e6e6fa);
	color: #4a4a4a;
	border: 2px solid #a9a9a9;
}

.badge-bronze {
	background: linear-gradient(135deg, #cd7f32, #daa520);
	color: #654321;
	border: 2px solid #b8860b;
}

/* Tab enhancements */
.tab-active {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: white;
}

/* Fun text styles */
.text-rainbow {
	background: linear-gradient(
		45deg,
		#ff6b6b,
		#4ecdc4,
		#45b7d1,
		#96ceb4,
		#ffeaa7,
		#dda0dd
	);
	-webkit-background-clip: text;
	background-clip: text;
	-webkit-text-fill-color: transparent;
	animation: rainbow-text 3s linear infinite;
	background-size: 200% 200%;
}

@keyframes rainbow-text {
	0% {
		background-position: 0% 50%;
	}
	50% {
		background-position: 100% 50%;
	}
	100% {
		background-position: 0% 50%;
	}
}
