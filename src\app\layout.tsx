import type {Metada<PERSON>} from 'next';
import './globals.css';
import {AuthProvider} from '@/lib/auth/provider';
import {Toaster} from '@/components/ui/toaster';

export const metadata: Metadata = {
	title: 'ScholarAI - FERPA-Compliant Educational Platform',
	description:
		'Safe, engaging, and compliant AI-powered learning for K-12 students',
	keywords: 'education, AI, FERPA, learning, K-12, students, safe learning',
	authors: [{name: 'ScholarAI Team'}],
	robots: 'index, follow',
	openGraph: {
		title: 'ScholarAI - Safe AI Learning Platform',
		description:
			'FERPA-compliant educational platform with AI-powered learning tools',
		type: 'website',
	},
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang='en'>
			<head>
				<link rel='preconnect' href='https://fonts.googleapis.com' />
				<link
					rel='preconnect'
					href='https://fonts.gstatic.com'
					crossOrigin='anonymous'
				/>
				<link
					href='https://fonts.googleapis.com/css2?family=PT+Sans:ital,wght@0,400;0,700;1,400;1,700&family=Space+Grotesk:wght@300..700&display=swap'
					rel='stylesheet'
				/>
			</head>
			<body className='font-body antialiased bg-background'>
				<AuthProvider>
					{children}
					<Toaster />
				</AuthProvider>
			</body>
		</html>
	);
}
