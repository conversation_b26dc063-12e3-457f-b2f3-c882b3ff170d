'use client';

import {useSession} from 'next-auth/react';
import {useRouter} from 'next/navigation';
import {useEffect} from 'react';
import MainApp from '@/components/main-app';
import { Loader2 } from 'lucide-react';

export default function HomePage() {
	const {data: session, status} = useSession();
	const router = useRouter();

	useEffect(() => {
		if (status === 'loading') return; // Still loading

		if (!session) {
			// User is not authenticated, redirect to welcome page
			router.push('/welcome');
		}
	}, [session, status, router]);

	// Show loading while checking authentication
	if (status === 'loading') {
		return (
			<div className='flex min-h-screen items-center justify-center'>
				<div className='text-center'>
					<Loader2 className="h-12 w-12 animate-spin text-primary mx-auto mb-4" />
					<p className='text-muted-foreground'>Loading ScholarAI...</p>
				</div>
			</div>
		);
	}

	// If user is authenticated, show the main app
	if (session) {
		return <MainApp />;
	}

	// This will be shown briefly before redirect
	return (
		<div className='flex min-h-screen items-center justify-center'>
			<div className='text-center'>
				<Loader2 className="h-12 w-12 animate-spin text-primary mx-auto mb-4" />
				<p className='text-muted-foreground'>Redirecting to welcome page...</p>
			</div>
		</div>
	);
}
