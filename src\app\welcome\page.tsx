'use client';

import {<PERSON><PERSON>} from '@/components/ui/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';
import {BrainCircuit, BookOpen, Shield, Users} from 'lucide-react';
import Link from 'next/link';

export default function WelcomePage() {
	return (
		<main className='flex min-h-screen flex-col items-center justify-center p-4 bg-gradient-to-br from-blue-50 to-indigo-100'>
			<div className='w-full max-w-4xl mx-auto text-center'>
				{/* Hero Section */}
				<div className='mb-12'>
					<div className='mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-primary'>
						<BrainCircuit className='h-10 w-10 text-primary-foreground' />
					</div>
					<h1 className='font-headline text-5xl font-bold text-primary mb-4'>
						Welcome to ScholarAI
					</h1>
					<p className='text-xl text-muted-foreground max-w-2xl mx-auto'>
						Your FERPA-compliant AI learning assistant. Safe, engaging, and
						personalized education for students of all ages.
					</p>
				</div>

				{/* Features Grid */}
				<div className='grid md:grid-cols-3 gap-6 mb-12'>
					<Card className='text-center'>
						<CardHeader>
							<div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100'>
								<BookOpen className='h-6 w-6 text-blue-600' />
							</div>
							<CardTitle>AI-Powered Learning</CardTitle>
						</CardHeader>
						<CardContent>
							<CardDescription>
								Personalized quizzes, flashcards, and study materials generated
								just for you.
							</CardDescription>
						</CardContent>
					</Card>

					<Card className='text-center'>
						<CardHeader>
							<div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-green-100'>
								<Shield className='h-6 w-6 text-green-600' />
							</div>
							<CardTitle>FERPA Compliant</CardTitle>
						</CardHeader>
						<CardContent>
							<CardDescription>
								Built with education privacy laws in mind. Your data is safe and
								secure.
							</CardDescription>
						</CardContent>
					</Card>

					<Card className='text-center'>
						<CardHeader>
							<div className='mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-purple-100'>
								<Users className='h-6 w-6 text-purple-600' />
							</div>
							<CardTitle>Age-Appropriate</CardTitle>
						</CardHeader>
						<CardContent>
							<CardDescription>
								Content tailored to your age group with appropriate safety
								filters.
							</CardDescription>
						</CardContent>
					</Card>
				</div>

				{/* CTA Buttons */}
				<div className='space-y-4'>
					<div className='flex flex-col sm:flex-row gap-4 justify-center'>
						<Link href='/auth/signup'>
							<Button size='lg' className='w-full sm:w-auto'>
								Get Started - Sign Up
							</Button>
						</Link>
						<Link href='/auth/signin'>
							<Button variant='outline' size='lg' className='w-full sm:w-auto'>
								Sign In
							</Button>
						</Link>
					</div>
					<p className='text-sm text-muted-foreground'>
						Join thousands of students and educators using ScholarAI
					</p>
				</div>

				{/* Footer Links */}
				<div className='mt-12 pt-8 border-t border-gray-200'>
					<div className='flex flex-wrap justify-center gap-6 text-sm text-muted-foreground'>
						<Link href='/terms' className='hover:text-primary hover:underline'>
							Terms of Service
						</Link>
						<Link
							href='/privacy'
							className='hover:text-primary hover:underline'>
							Privacy Policy
						</Link>
						<Link href='/about' className='hover:text-primary hover:underline'>
							About ScholarAI
						</Link>
						<Link
							href='/contact'
							className='hover:text-primary hover:underline'>
							Contact Us
						</Link>
					</div>
				</div>
			</div>
		</main>
	);
}


