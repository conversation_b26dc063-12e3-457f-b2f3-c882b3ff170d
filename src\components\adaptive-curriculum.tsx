'use client';

import {useState} from 'react';
import {But<PERSON>} from '@/components/ui/button';
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from '@/components/ui/card';
import {Input} from '@/components/ui/input';
import {Textarea} from '@/components/ui/textarea';
import {Label} from '@/components/ui/label';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/select';
import {Loader2, ListChecks} from 'lucide-react';
import {LessonPlan, Material} from '@/lib/types';
import {useToast} from '@/hooks/use-toast';

interface AdaptiveCurriculumProps {
  selectedMaterial: Material | null;
}

export default function AdaptiveCurriculum({selectedMaterial}: AdaptiveCurriculumProps) {
  const {toast} = useToast();
  const [goals, setGoals] = useState('Master the basics and be able to solve beginner problems.');
  const [skillLevel, setSkillLevel] = useState<'beginner' | 'intermediate' | 'advanced'>('beginner');
  const [learningStyle, setLearningStyle] = useState<'visual' | 'auditory' | 'reading-writing' | 'kinesthetic'>('visual');
  const [timePerWeek, setTimePerWeek] = useState<number>(5);
  const [durationWeeks, setDurationWeeks] = useState<number>(4);
  const [plan, setPlan] = useState<LessonPlan | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  const handleGenerate = async () => {
    setIsLoading(true);
    setPlan(null);
    try {
      const response = await fetch('/api/lesson-plan/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          goals,
          skillLevel,
          contextMaterial: selectedMaterial?.content ?? '',
          timePerWeekHours: timePerWeek,
          durationWeeks,
          learningStyle,
        }),
      });

      const data = await response.json();
      if (!data.success) {
        throw new Error(data.error || 'Failed to generate lesson plan');
      }
      setPlan(data.data as LessonPlan);
    } catch (err) {
      console.error(err);
      toast({
        title: 'Generation Failed',
        description: 'Could not generate a lesson plan. Please adjust inputs and try again.',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="grid gap-6 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Adaptive Curriculum</CardTitle>
          <CardDescription>Generate a personalized lesson plan based on your goals and level.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="goals">Learning goals</Label>
            <Textarea id="goals" value={goals} onChange={(e) => setGoals(e.target.value)} placeholder="Describe what you want to achieve" />
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label>Skill level</Label>
              <Select value={skillLevel} onValueChange={(v) => setSkillLevel(v as typeof skillLevel)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="beginner">Beginner</SelectItem>
                  <SelectItem value="intermediate">Intermediate</SelectItem>
                  <SelectItem value="advanced">Advanced</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Learning style</Label>
              <Select value={learningStyle} onValueChange={(v) => setLearningStyle(v as typeof learningStyle)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select style" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="visual">Visual</SelectItem>
                  <SelectItem value="auditory">Auditory</SelectItem>
                  <SelectItem value="reading-writing">Reading/Writing</SelectItem>
                  <SelectItem value="kinesthetic">Kinesthetic</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="time">Hours per week</Label>
              <Input id="time" type="number" min={1} max={40} value={timePerWeek} onChange={(e) => setTimePerWeek(parseInt(e.target.value || '0', 10))} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="weeks">Duration (weeks)</Label>
              <Input id="weeks" type="number" min={1} max={52} value={durationWeeks} onChange={(e) => setDurationWeeks(parseInt(e.target.value || '0', 10))} />
            </div>
          </div>
          <Button onClick={handleGenerate} disabled={isLoading} className="w-full">
            {isLoading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <ListChecks className="mr-2 h-4 w-4" />}
            Generate Plan
          </Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Lesson Plan</CardTitle>
          <CardDescription>{plan ? plan.title : 'Your plan will appear here.'}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {isLoading && (
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              Generating...
            </div>
          )}
          {!isLoading && plan && (
            <div className="space-y-4">
              <div>
                <p className="text-sm text-muted-foreground">Level: {plan.level}</p>
                <p className="mt-2 whitespace-pre-wrap">{plan.overview}</p>
              </div>
              <div>
                <h4 className="font-semibold">Objectives</h4>
                <ul className="list-disc pl-5 text-sm">
                  {plan.objectives.map((o, i) => (
                    <li key={i}>{o}</li>
                  ))}
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold">Schedule</h4>
                <div className="space-y-3">
                  {plan.schedule.map((w) => (
                    <div key={w.week} className="rounded-md border p-3">
                      <div className="font-medium">Week {w.week}: {w.focus}</div>
                      <ul className="mt-2 list-disc pl-5 text-sm">
                        {w.sessions.map((s, idx) => (
                          <li key={idx}>
                            <span className="font-medium">{s.title}</span> — {s.estimatedTimeHours}h
                            <ul className="list-disc pl-5">
                              {s.activities.map((a, j) => (
                                <li key={j}>{a}</li>
                              ))}
                            </ul>
                          </li>
                        ))}
                      </ul>
                    </div>
                  ))}
                </div>
              </div>
              {!!plan.resources?.length && (
                <div>
                  <h4 className="font-semibold">Resources</h4>
                  <ul className="list-disc pl-5 text-sm">
                    {plan.resources.map((r, i) => (
                      <li key={i}>{r}</li>
                    ))}
                  </ul>
                </div>
              )}
              {!!plan.assessments?.length && (
                <div>
                  <h4 className="font-semibold">Assessments</h4>
                  <ul className="list-disc pl-5 text-sm">
                    {plan.assessments.map((r, i) => (
                      <li key={i}>{r}</li>
                    ))}
                  </ul>
                </div>
              )}
              {!!plan.studyTips?.length && (
                <div>
                  <h4 className="font-semibold">Study Tips</h4>
                  <ul className="list-disc pl-5 text-sm">
                    {plan.studyTips.map((r, i) => (
                      <li key={i}>{r}</li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}


