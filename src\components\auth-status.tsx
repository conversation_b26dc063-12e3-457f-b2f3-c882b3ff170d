'use client';

import {useSession} from 'next-auth/react';
import {Card, CardContent, CardHeader, CardTitle} from '@/components/ui/card';
import {Badge} from '@/components/ui/badge';

export default function AuthStatus() {
	const {data: session, status} = useSession();

	if (status === 'loading') {
		return (
			<Card className='w-full max-w-md'>
				<CardHeader>
					<CardTitle>Authentication Status</CardTitle>
				</CardHeader>
				<CardContent>
					<p>Loading...</p>
				</CardContent>
			</Card>
		);
	}

	if (!session) {
		return (
			<Card className='w-full max-w-md'>
				<CardHeader>
					<CardTitle>Authentication Status</CardTitle>
				</CardHeader>
				<CardContent>
					<Badge variant='destructive'>Not Authenticated</Badge>
					<p className='mt-2 text-sm text-muted-foreground'>
						Please sign in to access the application.
					</p>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card className='w-full max-w-md'>
			<CardHeader>
				<CardTitle>Authentication Status</CardTitle>
			</CardHeader>
			<CardContent className='space-y-2'>
				<Badge variant='default'>Authenticated</Badge>
				<div className='space-y-1 text-sm'>
					<p>
						<strong>Email:</strong> {session.user?.email}
					</p>
					<p>
						<strong>Role:</strong> {session.user?.role}
					</p>
					<p>
						<strong>Age Group:</strong> {session.user?.ageGroup}
					</p>
					<p>
						<strong>Is Minor:</strong> {session.user?.isMinor ? 'Yes' : 'No'}
					</p>
					<p>
						<strong>Parental Consent:</strong>{' '}
						{session.user?.parentalConsentGiven ? 'Given' : 'Not Given'}
					</p>
				</div>
			</CardContent>
		</Card>
	);
}
