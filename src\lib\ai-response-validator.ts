import {
	ContentSafetyFilter,
	SafetyResult,
	ContentSafetyError,
} from './content-safety';
import {z} from 'zod';

// AI response validation types
export interface ValidationResult {
	isValid: boolean;
	issues: string[];
	suggestions: string[];
	educationalValue: number;
	ageAppropriate: boolean;
	safetyScore: number;
}

export interface ValidationConfig {
	context: 'quiz' | 'chat' | 'flashcard' | 'general';
	ageGroup: 'elementary' | 'middle' | 'high' | 'adult';
	strictMode: boolean;
	requireEducationalValue: boolean;
}

// Context-specific validation rules
const CONTEXT_RULES = {
	quiz: {
		maxLength: 2000,
		minLength: 50,
		requiredElements: ['question', 'options', 'answer'],
		format: 'structured-json',
		educationalValueThreshold: 0.3, // Lowered threshold
	},
	chat: {
		maxLength: 500,
		minLength: 10,
		requiredElements: ['helpful-response'],
		format: 'conversational',
		educationalValueThreshold: 0.2, // Lowered threshold
	},
	flashcard: {
		maxLength: 200,
		minLength: 10,
		requiredElements: ['term', 'definition'],
		format: 'structured-json',
		educationalValueThreshold: 0.4, // Lowered threshold
	},
	general: {
		maxLength: 1000,
		minLength: 10,
		requiredElements: [],
		format: 'text',
		educationalValueThreshold: 0.1, // Much lower threshold
	},
};

// Quiz-specific validation schemas
const QuizResponseSchema = z.object({
	questions: z
		.array(
			z.object({
				question: z.string().min(10).max(200),
				options: z.array(z.string()).min(2).max(6),
				correctAnswer: z.string(),
			})
		)
		.min(1)
		.max(20),
});

const FlashcardResponseSchema = z.object({
	cards: z
		.array(
			z.object({
				term: z.string().min(1).max(100),
				definition: z.string().min(10).max(300),
			})
		)
		.min(1)
		.max(50),
});

export class AIResponseValidator {
	private safetyFilter: ContentSafetyFilter;
	private config: ValidationConfig;

	constructor(config: ValidationConfig) {
		this.config = config;
		this.safetyFilter = new ContentSafetyFilter({
			strictMode: config.strictMode,
			ageGroup: config.ageGroup,
			educationalContext: true,
		});
	}

	/**
	 * Main validation function for AI responses
	 */
	async validateResponse(content: string): Promise<ValidationResult> {
		const issues: string[] = [];
		const suggestions: string[] = [];
		let educationalValue = 0;
		let ageAppropriate = true;
		let safetyScore = 1.0;

		try {
			// 1. Basic content safety check
			const safetyResult = await this.safetyFilter.checkContent(content);
			if (!safetyResult.isAppropriate) {
				issues.push(...safetyResult.violations);
				suggestions.push(...(safetyResult.suggestions || []));
				safetyScore = Math.max(0, 1.0 - safetyResult.violations.length * 0.3);
				ageAppropriate = false;
			}

			// 2. Context-specific validation
			const contextValidation = await this.validateContext(content);
			if (!contextValidation.isValid) {
				issues.push(...contextValidation.issues);
				suggestions.push(...contextValidation.suggestions);
			}

			// 3. Educational value assessment
			educationalValue = this.assessEducationalValue(content);
			if (
				this.config.requireEducationalValue &&
				educationalValue <
					CONTEXT_RULES[this.config.context].educationalValueThreshold
			) {
				issues.push('insufficient-educational-value');
				suggestions.push(
					'Add more educational content, examples, or learning context'
				);
			}

			// 4. Age-appropriateness specific to AI responses
			const ageValidation = this.validateAgeAppropriateness(content);
			if (!ageValidation.appropriate) {
				issues.push(...ageValidation.issues);
				suggestions.push(...ageValidation.suggestions);
				ageAppropriate = false;
			}

			// 5. Response quality check
			const qualityCheck = this.assessResponseQuality(content);
			if (!qualityCheck.isQuality) {
				issues.push(...qualityCheck.issues);
				suggestions.push(...qualityCheck.suggestions);
			}

			return {
				isValid: issues.length === 0,
				issues,
				suggestions,
				educationalValue,
				ageAppropriate,
				safetyScore,
			};
		} catch (error) {
			console.error('AI response validation error:', error);
			return {
				isValid: false,
				issues: ['validation-error'],
				suggestions: ['Please regenerate the response'],
				educationalValue: 0,
				ageAppropriate: false,
				safetyScore: 0,
			};
		}
	}

	/**
	 * Context-specific validation (quiz, chat, flashcard)
	 */
	private async validateContext(
		content: string
	): Promise<{isValid: boolean; issues: string[]; suggestions: string[]}> {
		const issues: string[] = [];
		const suggestions: string[] = [];
		const rules = CONTEXT_RULES[this.config.context];

		// Length validation
		if (content.length < rules.minLength) {
			issues.push('response-too-short');
			suggestions.push(
				`Response should be at least ${rules.minLength} characters`
			);
		}
		if (content.length > rules.maxLength) {
			issues.push('response-too-long');
			suggestions.push(
				`Response should be no more than ${rules.maxLength} characters`
			);
		}

		// Format-specific validation
		if (this.config.context === 'quiz') {
			const quizValidation = this.validateQuizFormat(content);
			if (!quizValidation.isValid) {
				issues.push(...quizValidation.issues);
				suggestions.push(...quizValidation.suggestions);
			}
		} else if (this.config.context === 'flashcard') {
			const flashcardValidation = this.validateFlashcardFormat(content);
			if (!flashcardValidation.isValid) {
				issues.push(...flashcardValidation.issues);
				suggestions.push(...flashcardValidation.suggestions);
			}
		} else if (this.config.context === 'chat') {
			const chatValidation = this.validateChatResponse(content);
			if (!chatValidation.isValid) {
				issues.push(...chatValidation.issues);
				suggestions.push(...chatValidation.suggestions);
			}
		}

		return {isValid: issues.length === 0, issues, suggestions};
	}

	/**
	 * Validate quiz JSON format and structure
	 */
	private validateQuizFormat(content: string): {
		isValid: boolean;
		issues: string[];
		suggestions: string[];
	} {
		const issues: string[] = [];
		const suggestions: string[] = [];

		try {
			const parsed = JSON.parse(content);
			const result = QuizResponseSchema.safeParse(parsed);

			if (!result.success) {
				issues.push('invalid-quiz-format');
				suggestions.push(
					'Ensure quiz has proper JSON structure with questions, options, and correct answers'
				);
				return {isValid: false, issues, suggestions};
			}

			// Additional quiz quality checks
			const quiz = result.data;
			for (let i = 0; i < quiz.questions.length; i++) {
				const question = quiz.questions[i];

				// Check if correct answer is in options
				if (!question.options.includes(question.correctAnswer)) {
					issues.push(`question-${i + 1}-invalid-answer`);
					suggestions.push(
						`Question ${
							i + 1
						}: Correct answer must be one of the provided options`
					);
				}

				// Check for question quality
				if (question.question.length < 10) {
					issues.push(`question-${i + 1}-too-short`);
					suggestions.push(
						`Question ${i + 1}: Make the question more detailed and clear`
					);
				}

				// Check for duplicate options
				const uniqueOptions = new Set(question.options);
				if (uniqueOptions.size !== question.options.length) {
					issues.push(`question-${i + 1}-duplicate-options`);
					suggestions.push(`Question ${i + 1}: All options should be unique`);
				}
			}
		} catch (error) {
			issues.push('invalid-json-format');
			suggestions.push('Response must be valid JSON format');
		}

		return {isValid: issues.length === 0, issues, suggestions};
	}

	/**
	 * Validate flashcard JSON format and structure
	 */
	private validateFlashcardFormat(content: string): {
		isValid: boolean;
		issues: string[];
		suggestions: string[];
	} {
		const issues: string[] = [];
		const suggestions: string[] = [];

		try {
			const parsed = JSON.parse(content);
			const result = FlashcardResponseSchema.safeParse(parsed);

			if (!result.success) {
				issues.push('invalid-flashcard-format');
				suggestions.push(
					'Ensure flashcards have proper JSON structure with terms and definitions'
				);
				return {isValid: false, issues, suggestions};
			}

			// Additional flashcard quality checks
			const flashcards = result.data;
			for (let i = 0; i < flashcards.cards.length; i++) {
				const card = flashcards.cards[i];

				if (card.definition.length < 10) {
					issues.push(`card-${i + 1}-definition-too-short`);
					suggestions.push(`Card ${i + 1}: Provide a more detailed definition`);
				}

				if (card.term.length < 2) {
					issues.push(`card-${i + 1}-term-too-short`);
					suggestions.push(`Card ${i + 1}: Term should be more descriptive`);
				}
			}
		} catch (error) {
			issues.push('invalid-json-format');
			suggestions.push('Response must be valid JSON format');
		}

		return {isValid: issues.length === 0, issues, suggestions};
	}

	/**
	 * Validate chat response quality and appropriateness
	 */
	private validateChatResponse(content: string): {
		isValid: boolean;
		issues: string[];
		suggestions: string[];
	} {
		const issues: string[] = [];
		const suggestions: string[] = [];

		// Check for helpful response indicators
		const helpfulPatterns = [
			/here's|here is|let me explain|to understand|for example/gi,
			/this means|in other words|simply put|basically/gi,
			/you can|try|consider|remember/gi,
		];

		const hasHelpfulLanguage = helpfulPatterns.some((pattern) =>
			pattern.test(content)
		);
		if (!hasHelpfulLanguage) {
			issues.push('response-not-helpful');
			suggestions.push('Make the response more helpful and educational');
		}

		// Check for personal advice (should be avoided)
		const personalAdvicePatterns = [
			/you should|you must|you need to/gi,
			/i recommend|i suggest|i think you/gi,
		];

		const hasPersonalAdvice = personalAdvicePatterns.some((pattern) =>
			pattern.test(content)
		);
		if (hasPersonalAdvice && this.config.strictMode) {
			issues.push('contains-personal-advice');
			suggestions.push(
				'Focus on educational facts rather than personal recommendations'
			);
		}

		return {isValid: issues.length === 0, issues, suggestions};
	}

	/**
	 * Assess educational value of the response
	 */
	private assessEducationalValue(content: string): number {
		const educationalIndicators = [
			/learn|understand|explain|concept|theory|principle/gi,
			/example|definition|facts|knowledge|study|academic/gi,
			/because|therefore|this shows|this means|as a result/gi,
			/history|science|math|literature|geography|biology/gi,
			/is|are|was|were|has|have|had|will|can|could|should|would/gi, // Common educational verbs
			/the|a|an|this|that|these|those/gi, // Articles and demonstratives (indicate structured content)
		];

		let score = 0;
		const words = content.split(/\s+/).length;

		educationalIndicators.forEach((pattern) => {
			const matches = content.match(pattern) || [];
			score += matches.length;
		});

		// More lenient scoring: any substantial response gets a base score
		const baseScore = Math.min(0.3, words * 0.01); // Base score for any substantial response
		const indicatorScore = Math.min(0.7, (score / words) * 5); // Reduced multiplier for indicators

		return Math.min(1, baseScore + indicatorScore);
	}

	/**
	 * Age-appropriateness validation for AI responses
	 */
	private validateAgeAppropriateness(content: string): {
		appropriate: boolean;
		issues: string[];
		suggestions: string[];
	} {
		const issues: string[] = [];
		const suggestions: string[] = [];

		// Check complexity based on age group
		const sentences = content
			.split(/[.!?]+/)
			.filter((s) => s.trim().length > 0).length;
		const words = content.split(/\s+/).length;
		const avgWordsPerSentence = words / Math.max(sentences, 1);

		const complexityLimits = {
			elementary: 15, // Increased for better educational content
			middle: 20, // Increased for better educational content
			high: 25, // Increased for better educational content
			adult: 30, // Increased for better educational content
		};

		if (avgWordsPerSentence > complexityLimits[this.config.ageGroup]) {
			issues.push('language-too-complex');
			suggestions.push(
				`Simplify language for ${this.config.ageGroup} level students`
			);
		}

		// Check for age-inappropriate topics
		if (this.config.ageGroup === 'elementary') {
			const complexTopics =
				/politics|political|religion|controversy|debate|war|conflict/gi;
			if (complexTopics.test(content)) {
				issues.push('topic-not-age-appropriate');
				suggestions.push(
					'Focus on simpler, more concrete topics for elementary students'
				);
			}
		}

		return {
			appropriate: issues.length === 0,
			issues,
			suggestions,
		};
	}

	/**
	 * Assess overall response quality
	 */
	private assessResponseQuality(content: string): {
		isQuality: boolean;
		issues: string[];
		suggestions: string[];
	} {
		const issues: string[] = [];
		const suggestions: string[] = [];

		// Check for coherence and completeness
		if (content.trim().length === 0) {
			issues.push('empty-response');
			suggestions.push('Provide a complete response');
			return {isQuality: false, issues, suggestions};
		}

		// Check for repetition
		const words = content.toLowerCase().split(/\s+/);
		const uniqueWords = new Set(words);
		const repetitionRatio = uniqueWords.size / words.length;

		if (repetitionRatio < 0.5 && words.length > 20) {
			issues.push('excessive-repetition');
			suggestions.push('Reduce repetitive content and add more variety');
		}

		// Check for incomplete sentences
		const sentences = content
			.split(/[.!?]+/)
			.filter((s) => s.trim().length > 0);
		const incompleteSentences = sentences.filter((s) => s.trim().length < 5);

		if (incompleteSentences.length > sentences.length * 0.3) {
			issues.push('incomplete-sentences');
			suggestions.push('Ensure all sentences are complete and clear');
		}

		return {isQuality: issues.length === 0, issues, suggestions};
	}

	/**
	 * Update validator configuration
	 */
	updateConfig(newConfig: Partial<ValidationConfig>): void {
		this.config = {...this.config, ...newConfig};
		this.safetyFilter.updateConfig({
			strictMode: this.config.strictMode,
			ageGroup: this.config.ageGroup,
			educationalContext: true,
		});
	}
}

// Utility function for quick validation
export async function validateAIResponse(
	content: string,
	context: ValidationConfig['context'],
	ageGroup: ValidationConfig['ageGroup'] = 'middle'
): Promise<ValidationResult> {
	const validator = new AIResponseValidator({
		context,
		ageGroup,
		strictMode: false, // Made less strict for educational content
		requireEducationalValue: false, // Made less strict for educational content
	});

	return validator.validateResponse(content);
}

// Export types are already defined as interfaces above
