import {NextAuthOptions} from 'next-auth';
import {Dr<PERSON><PERSON>Adapter} from '@auth/drizzle-adapter';
import EmailProvider from 'next-auth/providers/email';
import GoogleProvider from 'next-auth/providers/google';
import {db} from '@/lib/db/connection';
import {
	users,
	auditLogs,
	user,
	accounts,
	sessions,
	verificationTokens,
} from '@/lib/db/schema';
import {eq} from 'drizzle-orm';

export const authOptions: NextAuthOptions = {
	adapter: DrizzleAdapter(db, {
		usersTable: user,
		accountsTable: accounts,
		sessionsTable: sessions,
		verificationTokensTable: verificationTokens,
	}),

	providers: [
		// Email-based authentication (FERPA-friendly)
		EmailProvider({
			server: {
				host: process.env.EMAIL_SERVER_HOST,
				port: parseInt(process.env.EMAIL_SERVER_PORT || '587'),
				auth: {
					user: process.env.EMAIL_SERVER_USER,
					pass: process.env.EMAIL_SERVER_PASSWORD,
				},
			},
			from: process.env.EMAIL_FROM,
		}),

		// Google OAuth (with parental consent verification)
		GoogleProvider({
			clientId: process.env.GOOGLE_CLIENT_ID!,
			clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
			authorization: {
				params: {
					scope: 'openid email profile',
					prompt: 'consent',
				},
			},
		}),
	],

	session: {
		strategy: 'jwt',
		maxAge: 24 * 60 * 60, // 24 hours
		updateAge: 60 * 60, // 1 hour
	},

	jwt: {
		maxAge: 24 * 60 * 60, // 24 hours
	},

	pages: {
		signIn: '/auth/signin',
		verifyRequest: '/auth/verify-request',
		error: '/auth/error',
	},

	callbacks: {
		async signIn({user, account, profile}) {
			try {
				// Check if user exists in custom users table for FERPA compliance
				const existingUser = await db
					.select()
					.from(users)
					.where(eq(users.email, user.email!))
					.limit(1);

				// For audit logging, use the actual user ID if available, otherwise use email
				const userIdForAudit =
					existingUser.length > 0 ? existingUser[0].id : null;

				// FERPA compliance: Log authentication attempts
				await db.insert(auditLogs).values({
					userId: userIdForAudit,
					action: 'LOGIN_ATTEMPT',
					entityType: 'user',
					entityId: user.email!, // Use email as entity identifier
					description: `User ${user.email} attempted to sign in`,
					success: true,
					ipAddress: '127.0.0.1', // Will be updated with actual IP in middleware
					method: 'POST',
					endpoint: '/auth/signin',
				});

				if (existingUser.length > 0) {
					const userRecord = existingUser[0];

					// Check FERPA compliance for minors
					if (userRecord.isMinor && !userRecord.parentalConsentGiven) {
						console.warn(
							`Minor user ${user.email} attempted login without parental consent`
						);
						return false;
					}

					// Update last login
					await db
						.update(users)
						.set({
							lastLogin: new Date(),
							updatedAt: new Date(),
						})
						.where(eq(users.id, userRecord.id));
				}

				return true;
			} catch (error) {
				console.error('SignIn callback error:', error);
				return false;
			}
		},

		async jwt({token, user, account}) {
			// Initial sign in
			if (user) {
				try {
					const userRecord = await db
						.select()
						.from(users)
						.where(eq(users.email, user.email!))
						.limit(1);

					if (userRecord.length > 0) {
						const userData = userRecord[0];

						// Derive age/ageGroup from birthDate when available
						let derivedAgeGroup = userData.ageGroup || 'middle';
						let derivedIsMinor = userData.isMinor || false;
						if (userData.birthDate) {
							const dob = new Date(userData.birthDate as unknown as string);
							if (!Number.isNaN(dob.getTime())) {
								const today = new Date();
								let age = today.getFullYear() - dob.getFullYear();
								const m = today.getMonth() - dob.getMonth();
								if (m < 0 || (m === 0 && today.getDate() < dob.getDate()))
									age--;
								derivedIsMinor = age < 13;
								if (age < 11) derivedAgeGroup = 'elementary';
								else if (age < 14) derivedAgeGroup = 'middle';
								else if (age < 19) derivedAgeGroup = 'high';
								else derivedAgeGroup = 'adult';
							}
						}

						token.userId = userData.id;
						token.role = userData.role;
						token.accountType = userData.accountType;
						token.ageGroup = derivedAgeGroup;
						token.isMinor = derivedIsMinor;
						token.parentalConsentGiven = userData.parentalConsentGiven || false;
						token.contentFilterLevel = userData.contentFilterLevel || 'strict';
						token.organizationId = null; // Will be set based on membership
					}
				} catch (error) {
					console.error('JWT callback error:', error);
				}
			}

			return token;
		},

		async session({session, token}) {
			// Send properties to the client
			if (token) {
				session.user.id = token.userId as string;
				session.user.role = token.role as string;
				session.user.accountType = token.accountType as string;
				session.user.ageGroup = token.ageGroup as string;
				session.user.isMinor = token.isMinor as boolean;
				session.user.parentalConsentGiven =
					token.parentalConsentGiven as boolean;
				session.user.contentFilterLevel = token.contentFilterLevel as string;
				session.user.organizationId = token.organizationId as string | null;
			}

			return session;
		},
	},

	events: {
		async signIn({user, account, profile, isNewUser}) {
			try {
				// Look up the actual user ID from our custom users table
				const existingUser = await db
					.select()
					.from(users)
					.where(eq(users.email, user.email!))
					.limit(1);

				const userIdForAudit =
					existingUser.length > 0 ? existingUser[0].id : null;

				// Log successful sign-in for FERPA compliance
				await db.insert(auditLogs).values({
					userId: userIdForAudit,
					action: 'LOGIN_SUCCESS',
					entityType: 'user',
					entityId: user.email!, // Use email as entity identifier
					description: `User ${user.email} successfully signed in`,
					success: true,
					legalBasis: 'educational_purpose',
					dataClassification: 'internal',
				});
			} catch (error) {
				console.error('SignIn event error:', error);
			}
		},

		async signOut({token}) {
			// Log sign-out for FERPA compliance
			if (token?.userId) {
				await db.insert(auditLogs).values({
					userId: token.userId as string,
					action: 'LOGOUT',
					entityType: 'user',
					entityId: token.userId as string,
					description: `User signed out`,
					success: true,
				});
			}
		},
	},

	debug: process.env.NODE_ENV === 'development' && !process.env.NEXTAUTH_DEBUG,
};
