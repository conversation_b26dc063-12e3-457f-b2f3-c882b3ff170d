import {getServerSession} from 'next-auth';
import type {Session} from 'next-auth';
import {NextResponse} from 'next/server';
import {authOptions} from './config';

export async function getAuthSession(): Promise<Session | null> {
	return getServerSession(authOptions);
}

export type RequireApiSessionResult =
	| {ok: true; session: Session}
	| {ok: false; response: NextResponse};

export async function requireApiSession(): Promise<RequireApiSessionResult> {
	const session = await getServerSession(authOptions);
	if (!session) {
		return {
			ok: false,
			response: NextResponse.json(
				{success: false, error: 'Unauthorized'},
				{status: 401}
			),
		};
	}
	return {ok: true, session};
}
