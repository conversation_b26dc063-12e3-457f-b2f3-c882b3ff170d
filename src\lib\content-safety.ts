import {z} from 'zod';

// Content safety types
export interface SafetyResult {
	isAppropriate: boolean;
	violations: string[];
	confidence: number;
	riskLevel: 'low' | 'medium' | 'high';
	suggestions?: string[];
	debugInfo?: {
		triggeredPatterns: string[];
		educationalScore: number;
		complexityLevel: number;
		contextExceptions: string[];
	};
}

export interface FilterConfig {
	strictMode: boolean;
	ageGroup: 'elementary' | 'middle' | 'high' | 'adult';
	educationalContext: boolean;
	debugMode?: boolean;
}

// Educational context exceptions - words that are okay in educational settings
const EDUCATIONAL_CONTEXT_EXCEPTIONS = {
	// Historical/educational violence terms
	historical:
		/\b(world war|civil war|battle of|revolution|independence|freedom|liberation|resistance)\b/gi,
	// Scientific/medical terms
	scientific:
		/\b(medical|surgery|treatment|therapy|healing|recovery|health|medicine|doctor|patient)\b/gi,
	// Environmental/nature terms
	environmental:
		/\b(ecosystem|conservation|protection|preservation|wildlife|nature|environment)\b/gi,
	// Sports/competition terms
	sports:
		/\b(competition|tournament|championship|game|match|sport|athletic|olympic)\b/gi,
	// Literary/artistic terms
	literary:
		/\b(story|novel|poem|drama|tragedy|comedy|character|plot|theme|metaphor)\b/gi,
	// Political/educational terms
	political:
		/\b(democracy|republic|constitution|election|voting|citizenship|rights|freedom|liberty|justice)\b/gi,
};

// Content safety patterns - more nuanced for educational context
const SAFETY_PATTERNS = {
	// Inappropriate content patterns - more specific to avoid false positives
	violence: {
		// Explicit violence terms
		explicit:
			/\b(kill|murder|assassinate|execute|torture|abuse|rape|molest)\b/gi,
		// Weapons (but allow historical/educational context)
		weapons: /\b(gun|knife|bomb|explosive|missile|nuclear|chemical weapon)\b/gi,
		// Violent actions (but allow educational context)
		actions: /\b(attack|assault|bomb|explode|shoot|stab|punch|kick)\b/gi,
	},
	adult:
		/\b(sex|porn|adult|explicit|mature|inappropriate|nude|naked|sexual)\b/gi,
	hate: /\b(hate|racist|discrimination|bullying|harassment|offensive|slur|bigot)\b/gi,
	drugs:
		/\b(drug|alcohol|marijuana|cocaine|heroin|meth|smoking|drinking|beer|wine|drunk|intoxicated)\b/gi,
	profanity:
		/\b(damn|hell|crap|stupid|idiot|dumb|shut up|fuck|shit|bitch|ass)\b/gi,

	// Prompt injection patterns
	injection: [
		/ignore.+previous.+instructions/gi,
		/system.+prompt/gi,
		/act.+as.+(?:jailbreak|dan|evil)/gi,
		/forget.+everything/gi,
		/new.+instructions/gi,
		/override.+safety/gi,
	],

	// Educational positive patterns
	educational:
		/\b(learn|study|education|knowledge|academic|school|teach|homework|assignment|research|facts|concept|theory|principle|example|definition|explain|understand|analyze|discuss|process|system|method|technique|procedure|function|structure|component|element|factor|aspect|feature|characteristic|property|quality|condition|state|phase|stage|level|degree|amount|quantity|measure|unit|value|result|outcome|effect|impact|influence|role|purpose|goal|objective|aim|target|focus|emphasis|importance|significance|relevance|connection|relationship|link|association|correlation|cause|reason|basis|foundation|background|context|environment|setting|situation|circumstance|condition|case|instance|example|sample|model|pattern|form|shape|type|kind|category|class|group|set|collection|series|sequence|order|arrangement|organization|structure|framework|system|network|web|complex|simple|basic|advanced|fundamental|essential|key|main|primary|secondary|major|minor|significant|important|critical|crucial|vital|necessary|required|needed|essential|basic|fundamental|elementary|primary|secondary|higher|advanced|complex|simple|easy|difficult|challenging|straightforward|clear|obvious|apparent|evident|visible|noticeable|observable|measurable|quantifiable|assessable|evaluable|testable|verifiable|confirmable|demonstrable|provable|established|confirmed|verified|validated|accepted|recognized|acknowledged|understood|comprehended|grasped|mastered|learned|studied|researched|investigated|explored|examined|analyzed|evaluated|assessed|reviewed|considered|thought|reflected|pondered|contemplated|meditated|deliberated|debated|discussed|argued|reasoned|concluded|determined|decided|resolved|settled|established|created|developed|formed|built|constructed|assembled|organized|arranged|structured|designed|planned|prepared|arranged|scheduled|timed|coordinated|managed|controlled|directed|guided|led|supervised|monitored|tracked|followed|pursued|chased|sought|searched|found|discovered|identified|located|positioned|placed|situated|located|positioned|placed|situated|located|positioned|placed|situated)\b/gi,
	subjects:
		/\b(math|science|history|literature|geography|biology|chemistry|physics|english|art|music|language|social studies|civics|economics|philosophy|psychology)\b/gi,
	questions:
		/\b(who|what|when|where|why|how|which|whose|whom|explain|describe|define|compare|analyze|discuss|evaluate|identify|list|name|find|calculate|solve|determine|examine|investigate)\b/gi,
	historical:
		/\b(prime minister|president|king|queen|emperor|leader|government|politics|election|democracy|republic|monarchy|constitution|law|policy|minister|official|ruling|governing|administration|historical|ancient|medieval|modern|century|period|era|dynasty|empire|kingdom)\b/gi,
};

// Age-group specific filtering rules - more lenient for educational content
const AGE_GROUP_RULES = {
	elementary: {
		maxComplexity: 8, // Increased for better educational content
		allowedTopics: [
			'basic-science',
			'math',
			'reading',
			'art',
			'animals',
			'nature',
			'basic-history',
			'geography',
			'civics',
		],
		blockedTopics: [
			'explicit-violence',
			'adult-themes',
			'complex-political-issues',
		],
		strictProfanity: true,
		educationalExceptions: true, // Allow educational context exceptions
	},
	middle: {
		maxComplexity: 12, // Increased for better educational content
		allowedTopics: [
			'science',
			'history',
			'literature',
			'geography',
			'culture',
			'civics',
			'economics',
		],
		blockedTopics: ['explicit-content', 'graphic-violence'],
		strictProfanity: true,
		educationalExceptions: true,
	},
	high: {
		maxComplexity: 16, // Increased for better educational content
		allowedTopics: [
			'advanced-science',
			'philosophy',
			'economics',
			'social-studies',
			'political-science',
			'psychology',
		],
		blockedTopics: ['explicit-content'],
		strictProfanity: false,
		educationalExceptions: true,
	},
	adult: {
		maxComplexity: 20, // Increased for better educational content
		allowedTopics: ['all-academic'],
		blockedTopics: ['illegal-content'],
		strictProfanity: false,
		educationalExceptions: true,
	},
};

export class ContentSafetyFilter {
	private config: FilterConfig;

	constructor(
		config: FilterConfig = {
			strictMode: true,
			ageGroup: 'middle',
			educationalContext: true,
			debugMode: false,
		}
	) {
		this.config = config;
	}

	/**
	 * Main content safety check function
	 */
	async checkContent(text: string): Promise<SafetyResult> {
		const violations: string[] = [];
		let riskLevel: 'low' | 'medium' | 'high' = 'low';
		let confidence = 0.8;
		const debugInfo: any = {
			triggeredPatterns: [],
			educationalScore: 0,
			complexityLevel: 0,
			contextExceptions: [],
		};

		try {
			// 1. Check for inappropriate content with educational context awareness
			const inappropriateCheck = this.checkInappropriateContent(text);
			if (inappropriateCheck.violations.length > 0) {
				violations.push(...inappropriateCheck.violations);
				riskLevel = 'high';
				debugInfo.triggeredPatterns.push(...inappropriateCheck.violations);
			}

			// 2. Check for prompt injection attempts
			const injectionCheck = this.checkPromptInjection(text);
			if (injectionCheck.violations.length > 0) {
				violations.push(...injectionCheck.violations);
				riskLevel = 'high';
				confidence = 0.9;
				debugInfo.triggeredPatterns.push(...injectionCheck.violations);
			}

			// 3. Age-appropriateness check (more lenient for educational content)
			const ageCheck = this.checkAgeAppropriate(text);
			if (!ageCheck.isAppropriate) {
				violations.push(...ageCheck.violations);
				riskLevel = riskLevel === 'high' ? 'high' : 'medium';
				debugInfo.triggeredPatterns.push(...ageCheck.violations);
			}

			// 4. Educational value check (more lenient scoring)
			const educationalCheck = this.checkEducationalValue(text);
			debugInfo.educationalScore = educationalCheck.score;

			if (!educationalCheck.isEducational && this.config.educationalContext) {
				// Only flag if the content is clearly non-educational
				if (educationalCheck.score < 0.1) {
					// Much lower threshold - only flag obviously non-educational content
					violations.push('insufficient-educational-value');
					if (riskLevel === 'low') riskLevel = 'medium';
				}
			}

			// 5. Reading complexity check (more lenient)
			const complexityCheck = this.checkReadingComplexity(text);
			debugInfo.complexityLevel = complexityCheck.level;

			if (!complexityCheck.isAppropriate) {
				violations.push(`content-too-complex-for-${this.config.ageGroup}`);
				if (riskLevel === 'low') riskLevel = 'medium';
			}

			// 6. Apply educational context exceptions
			const contextExceptions = this.checkEducationalContextExceptions(text);
			debugInfo.contextExceptions = contextExceptions;

			// Remove violations that are covered by educational context exceptions
			const finalViolations = violations.filter((violation) => {
				return !contextExceptions.some((exception) =>
					violation.includes(exception.split('-')[0])
				);
			});

			return {
				isAppropriate: finalViolations.length === 0,
				violations: finalViolations,
				confidence,
				riskLevel: finalViolations.length === 0 ? 'low' : riskLevel,
				suggestions: this.generateSuggestions(finalViolations),
				debugInfo: this.config.debugMode ? debugInfo : undefined,
			};
		} catch (error) {
			console.error('Content safety check error:', error);
			return {
				isAppropriate: false,
				violations: ['safety-check-failed'],
				confidence: 0.1,
				riskLevel: 'high',
				suggestions: ['Please try uploading different content'],
				debugInfo: this.config.debugMode ? debugInfo : undefined,
			};
		}
	}

	/**
	 * Check for inappropriate content patterns with educational context awareness
	 */
	private checkInappropriateContent(text: string): {
		violations: string[];
		confidence: number;
	} {
		const violations: string[] = [];
		const lowerText = text.toLowerCase();

		// Check violence patterns with more specificity
		if (SAFETY_PATTERNS.violence.explicit.test(lowerText)) {
			violations.push('inappropriate-violence-explicit');
		}

		if (SAFETY_PATTERNS.violence.weapons.test(lowerText)) {
			violations.push('inappropriate-weapons');
		}

		if (SAFETY_PATTERNS.violence.actions.test(lowerText)) {
			violations.push('inappropriate-violent-actions');
		}

		// Check other inappropriate patterns
		if (SAFETY_PATTERNS.adult.test(lowerText)) {
			violations.push('inappropriate-adult');
		}

		if (SAFETY_PATTERNS.hate.test(lowerText)) {
			violations.push('inappropriate-hate');
		}

		if (SAFETY_PATTERNS.drugs.test(lowerText)) {
			violations.push('inappropriate-drugs');
		}

		// Special handling for profanity based on age group
		if (
			this.config.strictMode ||
			AGE_GROUP_RULES[this.config.ageGroup].strictProfanity
		) {
			if (SAFETY_PATTERNS.profanity.test(lowerText)) {
				violations.push('inappropriate-language');
			}
		}

		return {violations, confidence: 0.85};
	}

	/**
	 * Check for educational context exceptions
	 */
	private checkEducationalContextExceptions(text: string): string[] {
		const exceptions: string[] = [];

		for (const [category, pattern] of Object.entries(
			EDUCATIONAL_CONTEXT_EXCEPTIONS
		)) {
			if (pattern.test(text)) {
				exceptions.push(`educational-${category}`);
			}
		}

		return exceptions;
	}

	/**
	 * Check for prompt injection attempts
	 */
	private checkPromptInjection(text: string): {
		violations: string[];
		confidence: number;
	} {
		const violations: string[] = [];

		for (const pattern of SAFETY_PATTERNS.injection) {
			if (pattern.test(text)) {
				violations.push('prompt-injection-detected');
				break; // One detection is enough
			}
		}

		return {violations, confidence: 0.9};
	}

	/**
	 * Check age-appropriateness based on content and complexity (more lenient)
	 */
	private checkAgeAppropriate(text: string): {
		isAppropriate: boolean;
		violations: string[];
	} {
		const violations: string[] = [];
		const rules = AGE_GROUP_RULES[this.config.ageGroup];

		// Only check blocked topics for elementary students, and be more lenient
		if (this.config.ageGroup === 'elementary') {
			const suspiciousPatterns = [
				/explicit.+violence|graphic.+content/gi,
				/adult.+themes|mature.+content/gi,
			];

			for (const pattern of suspiciousPatterns) {
				if (pattern.test(text)) {
					violations.push('topic-not-age-appropriate');
					break;
				}
			}
		}

		return {
			isAppropriate: violations.length === 0,
			violations,
		};
	}

	/**
	 * Check if content has educational value (more lenient scoring)
	 */
	private checkEducationalValue(text: string): {
		isEducational: boolean;
		score: number;
	} {
		const educationalMatches = (text.match(SAFETY_PATTERNS.educational) || [])
			.length;
		const subjectMatches = (text.match(SAFETY_PATTERNS.subjects) || []).length;
		const questionMatches = (text.match(SAFETY_PATTERNS.questions) || [])
			.length;
		const historicalMatches = (text.match(SAFETY_PATTERNS.historical) || [])
			.length;

		const score =
			((educationalMatches +
				subjectMatches * 2 +
				questionMatches +
				historicalMatches) /
				Math.max(text.split(' ').length, 1)) *
			100;

		return {
			isEducational:
				score > 0.5 || // Much lower threshold for educational content
				educationalMatches >= 1 ||
				questionMatches >= 1 ||
				historicalMatches >= 1 ||
				subjectMatches >= 1 ||
				text.length > 20, // Any substantial response is considered educational
			score,
		};
	}

	/**
	 * Check reading complexity using simple heuristics (more lenient)
	 */
	private checkReadingComplexity(text: string): {
		isAppropriate: boolean;
		level: number;
	} {
		const sentences = text
			.split(/[.!?]+/)
			.filter((s) => s.trim().length > 0).length;
		const words = text.split(/\s+/).length;
		const avgWordsPerSentence = words / Math.max(sentences, 1);

		// Simple complexity estimation (Flesch-Kincaid approximation)
		const complexityLevel = Math.min(avgWordsPerSentence / 2, 15);
		const maxAllowed = AGE_GROUP_RULES[this.config.ageGroup].maxComplexity;

		return {
			isAppropriate: complexityLevel <= maxAllowed,
			level: complexityLevel,
		};
	}

	/**
	 * Generate helpful suggestions for content improvement
	 */
	private generateSuggestions(violations: string[]): string[] {
		const suggestions: string[] = [];

		if (violations.includes('inappropriate-violence-explicit')) {
			suggestions.push(
				'Consider removing explicit violent content and focusing on educational aspects'
			);
		}

		if (violations.includes('inappropriate-weapons')) {
			suggestions.push(
				'Consider focusing on historical or scientific context rather than weapon details'
			);
		}

		if (violations.includes('inappropriate-language')) {
			suggestions.push(
				'Please use appropriate language suitable for educational settings'
			);
		}

		if (violations.includes('non-educational-content')) {
			suggestions.push(
				'Try adding more educational context or learning objectives'
			);
		}

		if (violations.includes('prompt-injection-detected')) {
			suggestions.push(
				'Please provide straightforward educational content without special instructions'
			);
		}

		if (violations.some((v) => v.includes('too-complex'))) {
			suggestions.push(
				`Simplify the language for ${this.config.ageGroup} level students`
			);
		}

		return suggestions;
	}

	/**
	 * Update filter configuration
	 */
	updateConfig(newConfig: Partial<FilterConfig>): void {
		this.config = {...this.config, ...newConfig};
	}

	/**
	 * Get current filter statistics
	 */
	getFilterStats(): FilterConfig & {version: string} {
		return {
			...this.config,
			version: '1.1.0',
		};
	}

	/**
	 * Debug method to test content safety
	 */
	async debugContent(text: string): Promise<SafetyResult> {
		const originalDebugMode = this.config.debugMode;
		this.config.debugMode = true;
		const result = await this.checkContent(text);
		this.config.debugMode = originalDebugMode;
		return result;
	}
}

// Export singleton instance with default config
export const contentSafetyFilter = new ContentSafetyFilter();

// Custom error class for safety violations
export class ContentSafetyError extends Error {
	constructor(
		message: string,
		public violations: string[],
		public riskLevel: 'low' | 'medium' | 'high'
	) {
		super(message);
		this.name = 'ContentSafetyError';
	}
}

// Utility function for quick safety checks
export async function isContentSafe(
	text: string,
	config?: Partial<FilterConfig>
): Promise<boolean> {
	const filter = new ContentSafetyFilter(config as FilterConfig);
	const result = await filter.checkContent(text);
	return result.isAppropriate;
}

// Debug utility function
export async function debugContentSafety(
	text: string,
	config?: Partial<FilterConfig>
): Promise<SafetyResult> {
	const filter = new ContentSafetyFilter({
		...config,
		debugMode: true,
	} as FilterConfig);
	return await filter.checkContent(text);
}

// Export types are already defined as interfaces above
