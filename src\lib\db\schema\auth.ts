import {
	pgTable,
	uuid,
	varchar,
	timestamp,
	boolean,
	text,
	pgEnum,
	date,
} from 'drizzle-orm/pg-core';
import {sql} from 'drizzle-orm';

// User role enumeration for FERPA compliance
export const userRoleEnum = pgEnum('user_role', [
	'super_admin', // Platform administrators
	'org_admin', // School/district administrators
	'teacher', // Teachers and educators
	'parent_guardian', // Parent/guardian accounts
	'student', // Student accounts (13+ with parent consent)
]);

// Account type enumeration
export const accountTypeEnum = pgEnum('account_type', [
	'individual', // Individual family accounts
	'school', // School accounts
	'district', // School district accounts
	'organization', // Other educational organizations
]);

// Age group enumeration for content filtering
export const ageGroupEnum = pgEnum('age_group', [
	'pre_k', // Pre-K (3-4 years)
	'elementary', // Elementary (5-10 years)
	'middle', // Middle school (11-13 years)
	'high', // High school (14-18 years)
	'adult', // Adult learners (18+ years)
]);

// Users table - Core authentication and profile data
export const users = pgTable('users', {
	id: uuid('id').primaryKey().defaultRandom(),
	email: varchar('email', {length: 255}).unique().notNull(),
	emailVerified: timestamp('email_verified'),
	hashedPassword: varchar('hashed_password', {length: 255}),

	// Profile information
	firstName: varchar('first_name', {length: 100}),
	lastName: varchar('last_name', {length: 100}),
	displayName: varchar('display_name', {length: 200}),
	avatarUrl: text('avatar_url'),
	// Date of birth for age derivation (COPPA/COPPA)
	birthDate: date('birth_date'),

	// Role and permissions
	role: userRoleEnum('role').notNull().default('student'),
	accountType: accountTypeEnum('account_type').notNull().default('individual'),

	// FERPA compliance fields
	isMinor: boolean('is_minor').default(false), // Under 13 years old
	parentalConsentGiven: boolean('parental_consent_given').default(false),
	parentalConsentDate: timestamp('parental_consent_date'),
	consentDocument: text('consent_document'), // URL to signed consent form

	// Age-appropriate content filtering
	ageGroup: ageGroupEnum('age_group').default('elementary'),
	contentFilterLevel: varchar('content_filter_level', {length: 20}).default(
		'strict'
	),

	// Account status
	isActive: boolean('is_active').default(true),
	isEmailVerified: boolean('is_email_verified').default(false),
	lastLogin: timestamp('last_login'),

	// Audit fields
	createdAt: timestamp('created_at').defaultNow().notNull(),
	updatedAt: timestamp('updated_at').defaultNow().notNull(),
	createdBy: uuid('created_by'),

	// FERPA data retention
	dataRetentionDate: timestamp('data_retention_date'), // When data should be purged
	ferpaDirectoryInfo: boolean('ferpa_directory_info').default(false), // Directory info consent
});

// Parent-Child relationships for family accounts
export const parentChildRelations = pgTable('parent_child_relations', {
	id: uuid('id').primaryKey().defaultRandom(),
	parentId: uuid('parent_id')
		.references(() => users.id)
		.notNull(),
	childId: uuid('child_id')
		.references(() => users.id)
		.notNull(),

	// Relationship details
	relationshipType: varchar('relationship_type', {length: 50}).default(
		'parent'
	), // parent, guardian, etc.
	hasEducationalRights: boolean('has_educational_rights').default(true), // FERPA educational rights
	canViewProgress: boolean('can_view_progress').default(true),
	canManageAccount: boolean('can_manage_account').default(true),

	// Audit fields
	createdAt: timestamp('created_at').defaultNow().notNull(),
	updatedAt: timestamp('updated_at').defaultNow().notNull(),
	createdBy: uuid('created_by').references(() => users.id),
});

// User sessions for security tracking
export const userSessions = pgTable('user_sessions', {
	id: uuid('id').primaryKey().defaultRandom(),
	userId: uuid('user_id')
		.references(() => users.id)
		.notNull(),
	sessionToken: varchar('session_token', {length: 255}).unique().notNull(),

	// Session metadata
	ipAddress: varchar('ip_address', {length: 45}),
	userAgent: text('user_agent'),
	deviceInfo: text('device_info'),

	// Session lifecycle
	expiresAt: timestamp('expires_at').notNull(),
	lastActivityAt: timestamp('last_activity_at').defaultNow(),

	// Audit fields
	createdAt: timestamp('created_at').defaultNow().notNull(),
	revokedAt: timestamp('revoked_at'),
	revokedBy: uuid('revoked_by').references(() => users.id),
});

// Password reset tokens
export const passwordResetTokens = pgTable('password_reset_tokens', {
	id: uuid('id').primaryKey().defaultRandom(),
	userId: uuid('user_id')
		.references(() => users.id)
		.notNull(),
	token: varchar('token', {length: 255}).unique().notNull(),

	expiresAt: timestamp('expires_at').notNull(),
	usedAt: timestamp('used_at'),

	createdAt: timestamp('created_at').defaultNow().notNull(),
});

// Email verification tokens
export const emailVerificationTokens = pgTable('email_verification_tokens', {
	id: uuid('id').primaryKey().defaultRandom(),
	userId: uuid('user_id')
		.references(() => users.id)
		.notNull(),
	token: varchar('token', {length: 255}).unique().notNull(),
	email: varchar('email', {length: 255}).notNull(),

	expiresAt: timestamp('expires_at').notNull(),
	verifiedAt: timestamp('verified_at'),

	createdAt: timestamp('created_at').defaultNow().notNull(),
});
