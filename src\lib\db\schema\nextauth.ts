import {
	pgTable,
	uuid,
	varchar,
	timestamp,
	integer,
	primaryKey,
	text,
} from 'drizzle-orm/pg-core';
import {AdapterAccount} from 'next-auth/adapters';

// NextAuth required tables for <PERSON><PERSON>zleAdapter
// Note: DrizzleAdapter expects specific table and column names

// Users table (singular as expected by Dr<PERSON>zleAdapter)
export const user = pgTable('user', {
	id: uuid('id').primaryKey().defaultRandom(),
	name: varchar('name', {length: 255}),
	email: varchar('email', {length: 255}).unique().notNull(),
	emailVerified: timestamp('emailVerified', {mode: 'date'}),
	image: text('image'),
});

export const accounts = pgTable(
	'account',
	{
		userId: uuid('userId')
			.notNull()
			.references(() => user.id, {onDelete: 'cascade'}),
		type: varchar('type', {length: 255}).$type<AdapterAccount['type']>().notNull(),
		provider: varchar('provider', {length: 255}).notNull(),
		providerAccountId: varchar('providerAccountId', {length: 255}).notNull(),
		refresh_token: text('refresh_token'),
		access_token: text('access_token'),
		expires_at: integer('expires_at'),
		token_type: varchar('token_type', {length: 255}),
		scope: varchar('scope', {length: 255}),
		id_token: text('id_token'),
		session_state: varchar('session_state', {length: 255}),
	},
	(account) => ({
		compoundKey: primaryKey({
			columns: [account.provider, account.providerAccountId],
		}),
	})
);

export const sessions = pgTable('session', {
	sessionToken: varchar('sessionToken', {length: 255}).notNull().primaryKey(),
	userId: uuid('userId')
		.notNull()
		.references(() => user.id, {onDelete: 'cascade'}),
	expires: timestamp('expires', {mode: 'date'}).notNull(),
});

export const verificationTokens = pgTable(
	'verificationToken',
	{
		identifier: varchar('identifier', {length: 255}).notNull(),
		token: varchar('token', {length: 255}).notNull(),
		expires: timestamp('expires', {mode: 'date'}).notNull(),
	},
	(vt) => ({
		compoundKey: primaryKey({columns: [vt.identifier, vt.token]}),
	})
);