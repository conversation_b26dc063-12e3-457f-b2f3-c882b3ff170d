import {z} from 'zod';

const envSchema = z.object({
	GEMINI_API_KEY: z.string().min(1, 'GEMINI_API_KEY is required'),
	NODE_ENV: z
		.enum(['development', 'production', 'test'])
		.default('development'),

	// Database Configuration
	DATABASE_URL: z.string().min(1, 'DATABASE_URL is required'),
	DIRECT_URL: z.string().min(1, 'DIRECT_URL is required'),

	// Supabase Configuration
	NEXT_PUBLIC_SUPABASE_URL: z
		.string()
		.min(1, 'NEXT_PUBLIC_SUPABASE_URL is required'),
	NEXT_PUBLIC_SUPABASE_ANON_KEY: z
		.string()
		.min(1, 'NEXT_PUBLIC_SUPABASE_ANON_KEY is required'),
	SUPABASE_SERVICE_ROLE_KEY: z
		.string()
		.min(1, 'SUPABASE_SERVICE_ROLE_KEY is required'),

	// NextAuth Configuration
	NEXTAUTH_URL: z.string().min(1, 'NEXTAUTH_URL is required'),
	NEXTAUTH_SECRET: z.string().min(1, 'NEXTAUTH_SECRET is required'),

	// Google OAuth
	GOOGLE_CLIENT_ID: z.string().min(1, 'GOOGLE_CLIENT_ID is required'),
	GOOGLE_CLIENT_SECRET: z.string().min(1, 'GOOGLE_CLIENT_SECRET is required'),

	// Email Configuration
	EMAIL_SERVER_HOST: z.string().optional(),
	EMAIL_SERVER_PORT: z.string().optional(),
	EMAIL_SERVER_USER: z.string().optional(),
	EMAIL_SERVER_PASSWORD: z.string().optional(),
	EMAIL_FROM: z.string().optional(),

	// AI Configuration
	NEXT_PUBLIC_GOOGLE_AI_API_KEY: z.string().optional(),

	// Safety and Content Filtering
	CONTENT_SAFETY_STRICT_MODE: z
		.string()
		.optional()
		.transform((val) => val === 'true'),
	DEFAULT_AGE_GROUP: z
		.enum(['elementary', 'middle', 'high', 'adult'])
		.default('middle'),
	ENABLE_CONTENT_FILTERING: z
		.string()
		.optional()
		.transform((val) => val !== 'false'),

	// Compliance
	REQUIRE_PARENTAL_CONSENT: z
		.string()
		.optional()
		.transform((val) => val === 'true'),
	DATA_RETENTION_DAYS: z
		.string()
		.optional()
		.transform((val) => parseInt(val || '365')),

	// Optional: External safety APIs
	GOOGLE_PERSPECTIVE_API_KEY: z.string().optional(),
});

type Env = z.infer<typeof envSchema>;

function validateEnv(): Env {
	try {
		return envSchema.parse(process.env);
	} catch (error) {
		if (error instanceof z.ZodError) {
			const missingVars = error.errors
				.map((err) => err.path.join('.'))
				.join(', ');
			throw new Error(
				`Missing or invalid environment variables: ${missingVars}`
			);
		}
		throw error;
	}
}

export const env = validateEnv();
