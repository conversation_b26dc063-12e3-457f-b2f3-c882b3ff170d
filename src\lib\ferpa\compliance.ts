import {db} from '@/lib/db/connection';
import {
	auditLogs,
	consentRecords,
	dataRetentionSchedule,
} from '@/lib/db/schema';
import {eq, and, lte, gte} from 'drizzle-orm';

// FERPA compliance utilities for educational data protection
export class FERPACompliance {
	/**
	 * Log data access for FERPA audit requirements
	 */
	static async logDataAccess({
		userId,
		studentId,
		organizationId,
		action,
		entityType,
		entityId,
		description,
		legalBasis = 'educational_purpose',
		dataClassification = 'educational_record',
		ipAddress,
		userAgent,
		endpoint,
		success = true,
		errorMessage,
	}: {
		userId: string;
		studentId?: string;
		organizationId?: string;
		action: string;
		entityType: string;
		entityId: string;
		description: string;
		legalBasis?: string;
		dataClassification?: string;
		ipAddress?: string;
		userAgent?: string;
		endpoint?: string;
		success?: boolean;
		errorMessage?: string;
	}) {
		try {
			await db.insert(auditLogs).values({
				userId,
				organizationId,
				action,
				entityType,
				entityId,
				description,
				legalBasis,
				dataClassification,
				studentDataAccessed: !!studentId,
				ipAddress,
				userAgent,
				endpoint,
				success,
				errorMessage,
				riskLevel: this.calculateRiskLevel(
					action,
					entityType,
					dataClassification
				),
				dataRetentionDate: new Date(this.calculateRetentionDate()),
			});
		} catch (error) {
			console.error('Failed to log FERPA audit entry:', error);
			// Don't throw error to avoid breaking application flow
		}
	}

	/**
	 * Check if user has consent to access student data
	 */
	static async checkConsent(
		userId: string,
		studentId: string,
		purpose: string
	): Promise<boolean> {
		try {
			const consents = await db
				.select()
				.from(consentRecords)
				.where(
					and(
						eq(consentRecords.userId, userId),
						eq(consentRecords.studentId, studentId),
						eq(consentRecords.status, 'active'),
						eq(consentRecords.consentGiven, true)
					)
				);

			// Check if there's a valid consent for the specific purpose or general data processing
			return consents.some(
				(consent) =>
					consent.purpose.includes(purpose) ||
					consent.consentType === 'data_processing'
			);
		} catch (error) {
			console.error('Failed to check FERPA consent:', error);
			return false;
		}
	}

	/**
	 * Verify parental consent for minor students
	 */
	static async verifyParentalConsent(
		parentId: string,
		studentId: string
	): Promise<boolean> {
		try {
			const consent = await db
				.select()
				.from(consentRecords)
				.where(
					and(
						eq(consentRecords.userId, parentId),
						eq(consentRecords.studentId, studentId),
						eq(consentRecords.consentType, 'parental_consent'),
						eq(consentRecords.status, 'active'),
						eq(consentRecords.consentGiven, true)
					)
				)
				.limit(1);

			return consent.length > 0;
		} catch (error) {
			console.error('Failed to verify parental consent:', error);
			return false;
		}
	}

	/**
	 * Schedule data for retention/deletion per FERPA requirements
	 */
	static async scheduleDataRetention({
		entityType,
		entityId,
		organizationId,
		dataClassification,
		retentionPeriod = '7_years', // FERPA default
		legalRequirement = 'FERPA',
	}: {
		entityType: string;
		entityId: string;
		organizationId?: string;
		dataClassification: string;
		retentionPeriod?: string;
		legalRequirement?: string;
	}) {
		try {
			const createdDate = new Date();
			const retentionStartDate = new Date();
			const scheduledDeletionDate = this.calculateDeletionDate(retentionPeriod);

			await db.insert(dataRetentionSchedule).values({
				entityType,
				entityId,
				organizationId,
				dataClassification,
				retentionPeriod,
				legalRequirement,
				createdDate,
				retentionStartDate,
				scheduledDeletionDate,
			});
		} catch (error) {
			console.error('Failed to schedule data retention:', error);
		}
	}

	/**
	 * Get audit trail for a specific student (for FERPA requests)
	 */
	static async getStudentAuditTrail(
		studentId: string,
		organizationId?: string,
		startDate?: Date,
		endDate?: Date
	) {
		try {
			// Build conditions
			const conditions = [eq(auditLogs.studentDataAccessed, true)];

			if (organizationId) {
				conditions.push(eq(auditLogs.organizationId, organizationId));
			}

			if (startDate) {
				conditions.push(gte(auditLogs.createdAt, startDate));
			}

			if (endDate) {
				conditions.push(lte(auditLogs.createdAt, endDate));
			}

			const auditTrail = await db
				.select()
				.from(auditLogs)
				.where(and(...conditions));

			// Filter by student data involvement (entity IDs, descriptions, etc.)
			return auditTrail.filter(
				(log) =>
					log.entityId === studentId ||
					log.description?.includes(studentId) ||
					log.userId === studentId
			);
		} catch (error) {
			console.error('Failed to get student audit trail:', error);
			return [];
		}
	}

	/**
	 * Generate FERPA compliance report
	 */
	static async generateComplianceReport(
		organizationId: string,
		period: 'monthly' | 'quarterly' | 'annual'
	) {
		try {
			const endDate = new Date();
			const startDate = new Date();

			switch (period) {
				case 'monthly':
					startDate.setMonth(startDate.getMonth() - 1);
					break;
				case 'quarterly':
					startDate.setMonth(startDate.getMonth() - 3);
					break;
				case 'annual':
					startDate.setFullYear(startDate.getFullYear() - 1);
					break;
			}

			const auditLogs = await this.getAuditLogsForPeriod(
				organizationId,
				startDate,
				endDate
			);
			const consentRecords = await this.getConsentRecordsForPeriod(
				organizationId,
				startDate,
				endDate
			);
			const retentionSchedule = await this.getRetentionScheduleForPeriod(
				organizationId,
				startDate,
				endDate
			);

			return {
				period,
				startDate: startDate.toISOString(),
				endDate: endDate.toISOString(),
				organizationId,
				summary: {
					totalDataAccesses: auditLogs.length,
					studentDataAccesses: auditLogs.filter(
						(log) => log.studentDataAccessed
					).length,
					consentRecords: consentRecords.length,
					scheduledDeletions: retentionSchedule.filter(
						(r) => r.status === 'active'
					).length,
					completedDeletions: retentionSchedule.filter(
						(r) => r.status === 'deleted'
					).length,
				},
				auditLogs: auditLogs.slice(0, 1000), // Limit for performance
				consentRecords,
				retentionSchedule,
				generatedAt: new Date().toISOString(),
			};
		} catch (error) {
			console.error('Failed to generate compliance report:', error);
			throw error;
		}
	}

	/**
	 * Calculate risk level for audit logging
	 */
	private static calculateRiskLevel(
		action: string,
		entityType: string,
		dataClassification: string
	): string {
		// High-risk actions
		if (['DELETE', 'EXPORT', 'SHARE'].includes(action)) {
			return 'high';
		}

		// Medium-risk for sensitive data
		if (
			['educational_record', 'behavioral_data'].includes(dataClassification)
		) {
			return 'medium';
		}

		// Low-risk for directory information
		if (dataClassification === 'directory_info') {
			return 'low';
		}

		return 'medium';
	}

	/**
	 * Calculate retention date (7 years from creation for FERPA)
	 */
	private static calculateRetentionDate(): string {
		const retentionDate = new Date();
		retentionDate.setFullYear(retentionDate.getFullYear() + 7); // FERPA requirement
		return retentionDate.toISOString();
	}

	/**
	 * Calculate deletion date based on retention period
	 */
	private static calculateDeletionDate(retentionPeriod: string): Date {
		const deletionDate = new Date();

		switch (retentionPeriod) {
			case '3_years':
				deletionDate.setFullYear(deletionDate.getFullYear() + 3);
				break;
			case '5_years':
				deletionDate.setFullYear(deletionDate.getFullYear() + 5);
				break;
			case '7_years':
				deletionDate.setFullYear(deletionDate.getFullYear() + 7);
				break;
			case '10_years':
				deletionDate.setFullYear(deletionDate.getFullYear() + 10);
				break;
			case 'indefinite':
				deletionDate.setFullYear(deletionDate.getFullYear() + 100); // Effectively indefinite
				break;
			default:
				deletionDate.setFullYear(deletionDate.getFullYear() + 7); // Default to 7 years
		}

		return deletionDate;
	}

	/**
	 * Helper methods for compliance reporting
	 */
	private static async getAuditLogsForPeriod(
		organizationId: string,
		startDate: Date,
		endDate: Date
	) {
		return await db
			.select()
			.from(auditLogs)
			.where(
				and(
					eq(auditLogs.organizationId, organizationId),
					gte(auditLogs.createdAt, startDate),
					lte(auditLogs.createdAt, endDate)
				)
			);
	}

	private static async getConsentRecordsForPeriod(
		organizationId: string,
		startDate: Date,
		endDate: Date
	) {
		return await db
			.select()
			.from(consentRecords)
			.where(
				and(
					eq(consentRecords.organizationId, organizationId),
					gte(consentRecords.createdAt, startDate),
					lte(consentRecords.createdAt, endDate)
				)
			);
	}

	private static async getRetentionScheduleForPeriod(
		organizationId: string,
		startDate: Date,
		endDate: Date
	) {
		return await db
			.select()
			.from(dataRetentionSchedule)
			.where(
				and(
					eq(dataRetentionSchedule.organizationId, organizationId),
					gte(dataRetentionSchedule.createdAt, startDate),
					lte(dataRetentionSchedule.createdAt, endDate)
				)
			);
	}
}
