import {withAuth} from 'next-auth/middleware';
import {NextResponse} from 'next/server';
import type {NextRequest} from 'next/server';
import {FERPACompliance} from '@/lib/ferpa/compliance';

export default withAuth(
	async function middleware(req: NextRequest) {
		const token = (req as any).nextauth?.token;
		const {pathname} = req.nextUrl;

		// Log page access for FERPA compliance
		if (token?.userId) {
			await FERPACompliance.logDataAccess({
				userId: token.userId as string,
				action: 'PAGE_ACCESS',
				entityType: 'page',
				entityId: pathname,
				description: `User accessed ${pathname}`,
				ipAddress:
					req.headers.get('x-forwarded-for') ||
					req.headers.get('x-real-ip') ||
					'unknown',
				userAgent: req.headers.get('user-agent') || 'unknown',
				endpoint: pathname,
			});
		}

		// Protect admin routes
		if (pathname.startsWith('/admin')) {
			if (
				!token ||
				!['super_admin', 'org_admin'].includes(token.role as string)
			) {
				return NextResponse.redirect(new URL('/unauthorized', req.url));
			}
		}

		// Protect parent dashboard
		if (pathname.startsWith('/parent')) {
			if (!token || token.role !== 'parent_guardian') {
				return NextResponse.redirect(new URL('/unauthorized', req.url));
			}
		}

		// Protect teacher routes
		if (pathname.startsWith('/teacher')) {
			if (!token || !['teacher', 'org_admin'].includes(token.role as string)) {
				return NextResponse.redirect(new URL('/unauthorized', req.url));
			}
		}

		// Age-appropriate content filtering
		if (token?.isMinor && !token?.parentalConsentGiven) {
			// Redirect minors without parental consent to consent page
			if (!pathname.startsWith('/consent') && !pathname.startsWith('/auth')) {
				return NextResponse.redirect(new URL('/consent', req.url));
			}
		}

		return NextResponse.next();
	},
	{
		callbacks: {
			authorized: ({token, req}) => {
				const {pathname} = req.nextUrl;

				// Allow access to auth pages
				if (pathname.startsWith('/auth') || pathname.startsWith('/api/auth')) {
					return true;
				}

				// Allow access to public pages
				if (
					pathname === '/' ||
					pathname.startsWith('/public') ||
					pathname === '/welcome'
				) {
					return true;
				}

				// Allow access to API routes (for now - you may want to add authentication later)
				if (pathname.startsWith('/api/')) {
					return true;
				}

				// Require authentication for all other pages
				return !!token;
			},
		},
	}
);

export const config = {
	matcher: [
		/*
		 * Match all request paths except for the ones starting with:
		 * - api/public (public API routes)
		 * - _next/static (static files)
		 * - _next/image (image optimization files)
		 * - favicon.ico (favicon file)
		 * - public folder files
		 */
		'/((?!api/public|_next/static|_next/image|favicon.ico|public).*)',
	],
};
