const {ContentSafetyFilter} = require('./src/lib/content-safety.ts');
const {validateAIResponse} = require('./src/lib/ai-response-validator.ts');

// Test content safety with sample educational content
async function testContentSafety() {
	console.log('Testing Content Safety Filter...\n');

	const filter = new ContentSafetyFilter({
		strictMode: false,
		ageGroup: 'middle',
		educationalContext: true,
		debugMode: true,
	});

	// Test sample educational content
	const testContent = [
		"The water cycle is a continuous process where water moves from the Earth's surface to the atmosphere and back again. This process includes evaporation, condensation, precipitation, and collection.",
		'Photosynthesis is the process by which plants convert sunlight, carbon dioxide, and water into glucose and oxygen. This is essential for life on Earth.',
		'The American Revolution was a war fought between 1775 and 1783 between Great Britain and its thirteen colonies in North America. The colonies won their independence and formed the United States of America.',
		'In mathematics, the Pythagorean theorem states that in a right triangle, the square of the length of the hypotenuse is equal to the sum of the squares of the lengths of the other two sides.',
	];

	for (let i = 0; i < testContent.length; i++) {
		console.log(`Test ${i + 1}:`);
		console.log(`Content: "${testContent[i].substring(0, 100)}..."`);

		const result = await filter.checkContent(testContent[i]);
		console.log(`Result:`, {
			isAppropriate: result.isAppropriate,
			violations: result.violations,
			confidence: result.confidence,
			riskLevel: result.riskLevel,
			debugInfo: result.debugInfo,
		});
		console.log('---\n');
	}

	// Test AI response validation
	console.log('Testing AI Response Validation...\n');

	for (let i = 0; i < testContent.length; i++) {
		console.log(`AI Response Test ${i + 1}:`);
		console.log(`Content: "${testContent[i].substring(0, 100)}..."`);

		const result = await validateAIResponse(
			testContent[i],
			'general',
			'middle'
		);
		console.log(`Result:`, {
			isValid: result.isValid,
			issues: result.issues,
			educationalValue: result.educationalValue,
			ageAppropriate: result.ageAppropriate,
			safetyScore: result.safetyScore,
		});
		console.log('---\n');
	}
}

// Run the test
testContentSafety().catch(console.error);


