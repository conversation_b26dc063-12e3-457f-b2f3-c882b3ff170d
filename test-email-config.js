// Test Email Configuration
require('dotenv').config();

const nodemailer = require('nodemailer');

async function testEmailConfig() {
  console.log('Testing email configuration...\n');
  
  const config = {
    host: process.env.EMAIL_SERVER_HOST,
    port: parseInt(process.env.EMAIL_SERVER_PORT || '587'),
    secure: false, // true for 465, false for other ports
    auth: {
      user: process.env.EMAIL_SERVER_USER,
      pass: process.env.EMAIL_SERVER_PASSWORD,
    },
  };

  console.log('Email Configuration:');
  console.log(`Host: ${config.host}`);
  console.log(`Port: ${config.port}`);
  console.log(`User: ${config.auth.user}`);
  console.log(`Password: ${config.auth.pass ? '***SET***' : '***NOT SET***'}`);
  console.log(`From: ${process.env.EMAIL_FROM}\n`);

  if (!config.auth.user || config.auth.user === '<EMAIL>') {
    console.log('❌ Email configuration not set up properly!');
    console.log('Please update your .env file with actual email credentials.');
    return;
  }

  try {
    const transporter = nodemailer.createTransporter(config);
    
    // Verify connection configuration
    await transporter.verify();
    console.log('✅ Email configuration is valid!');
    
    // Send test email
    const info = await transporter.sendMail({
      from: process.env.EMAIL_FROM,
      to: config.auth.user, // Send to yourself
      subject: 'ScholarAI Email Test',
      text: 'This is a test email from ScholarAI to verify your email configuration.',
      html: '<p>This is a test email from ScholarAI to verify your email configuration.</p>',
    });

    console.log('✅ Test email sent successfully!');
    console.log(`Message ID: ${info.messageId}`);
    
  } catch (error) {
    console.log('❌ Email configuration error:');
    console.log(error.message);
    
    if (error.code === 'EAUTH') {
      console.log('\n💡 This is usually an authentication error. Make sure:');
      console.log('1. Your email and password are correct');
      console.log('2. For Gmail: You have 2FA enabled and are using an App Password');
      console.log('3. For other providers: Check your SMTP settings');
    }
  }
}

testEmailConfig();

